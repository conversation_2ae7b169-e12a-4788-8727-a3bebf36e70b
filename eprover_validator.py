"""
EProver验证器类
负责使用eprover验证TPTP文件的语法和逻辑正确性
"""

import os
import subprocess
import tempfile
import logging
from typing import Tuple, Optional
from pathlib import Path


class EProverValidator:
    """EProver验证器"""
    
    def __init__(self, eprover_path: str = "eprover"):
        """
        初始化EProver验证器

        Args:
            eprover_path (str): eprover可执行文件路径
        """
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(eprover_path):
            self.eprover_path = os.path.abspath(eprover_path)
        else:
            self.eprover_path = eprover_path
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 验证eprover是否可用
        self._check_eprover_availability()
    
    def _check_eprover_availability(self) -> bool:
        """检查eprover是否可用"""
        try:
            result = subprocess.run(
                [self.eprover_path, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.logger.info(f"EProver可用: {self.eprover_path}")
                return True
            else:
                self.logger.warning(f"EProver不可用，返回码: {result.returncode}")
                return False
                
        except FileNotFoundError:
            self.logger.error(f"未找到eprover: {self.eprover_path}")
            return False
        except subprocess.TimeoutExpired:
            self.logger.error("EProver版本检查超时")
            return False
        except Exception as e:
            self.logger.error(f"检查EProver时出错: {e}")
            return False
    
    def validate_tptp_content(self, tptp_content: str, filename: str = "temp.p") -> Tuple[bool, str, str]:
        """
        验证TPTP内容
        
        Args:
            tptp_content (str): TPTP内容
            filename (str): 临时文件名
            
        Returns:
            Tuple[bool, str, str]: (是否有效, 标准输出, 错误输出)
        """
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.p', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(tptp_content)
            temp_file_path = temp_file.name
        
        try:
            # 运行eprover
            is_valid, stdout, stderr = self._run_eprover(temp_file_path)
            
            self.logger.info(f"验证文件 {filename}: {'通过' if is_valid else '失败'}")
            
            return is_valid, stdout, stderr
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except OSError:
                pass
    
    def validate_tptp_file(self, file_path: str) -> Tuple[bool, str, str]:
        """
        验证TPTP文件
        
        Args:
            file_path (str): TPTP文件路径
            
        Returns:
            Tuple[bool, str, str]: (是否有效, 标准输出, 错误输出)
        """
        if not os.path.exists(file_path):
            return False, "", f"文件不存在: {file_path}"
        
        return self._run_eprover(file_path)
    
    def _run_eprover(self, file_path: str) -> Tuple[bool, str, str]:
        """
        运行eprover验证
        
        Args:
            file_path (str): TPTP文件路径
            
        Returns:
            Tuple[bool, str, str]: (是否有效, 标准输出, 错误输出)
        """
        try:
            # 构建eprover命令
            cmd = [
                self.eprover_path,
                "--auto",
                "--proof-object",
                os.path.abspath(file_path)
            ]
            
            self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            # 运行eprover
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,  # 30秒超时
                cwd=os.path.dirname(os.path.abspath(file_path))
            )
            
            stdout = result.stdout.strip()
            stderr = result.stderr.strip()
            
            # 分析输出判断是否有语法错误
            is_valid = self._analyze_eprover_output(result.returncode, stdout, stderr)
            
            return is_valid, stdout, stderr
            
        except subprocess.TimeoutExpired:
            error_msg = "EProver执行超时"
            self.logger.error(error_msg)
            return False, "", error_msg
            
        except Exception as e:
            error_msg = f"运行EProver时出错: {e}"
            self.logger.error(error_msg)
            return False, "", error_msg
    
    def _analyze_eprover_output(self, return_code: int, stdout: str, stderr: str) -> bool:
        """
        分析eprover输出判断是否有语法错误

        Args:
            return_code (int): 返回码
            stdout (str): 标准输出
            stderr (str): 错误输出

        Returns:
            bool: 是否语法正确（能够进行逻辑判定）
        """
        # 首先检查是否有明显的语法错误
        syntax_error_indicators = [
            "syntax error",
            "parse error",
            "lexical error",
            "unexpected token",
            "malformed",
            "invalid syntax",
            "cannot parse",
            "used with arity",  # 函数参数数量错误
            "registered with arity",
            "undeclared function",
            "undeclared predicate",
            "formula has free variables",  # 自由变量错误
            "check parentheses and quantifier precedence"  # 括号和量词优先级错误
        ]

        # 检查stderr中的语法错误
        stderr_lower = stderr.lower()
        for indicator in syntax_error_indicators:
            if indicator in stderr_lower:
                self.logger.debug(f"发现语法错误指示器: {indicator}")
                return False

        # 检查stdout中的语法错误
        stdout_lower = stdout.lower()
        for indicator in syntax_error_indicators:
            if indicator in stdout_lower:
                self.logger.debug(f"发现语法错误指示器: {indicator}")
                return False

        # 检查是否有SZS状态输出（表示eprover能够处理这个问题）
        if "# szs status" in stdout_lower:
            self.logger.debug("发现SZS状态，eprover能够处理此问题")
            return True

        # 检查是否有证明相关的输出
        proof_indicators = [
            "proof found",
            "completion found",
            "satisfiable",
            "unsatisfiable",
            "theorem",
            "countersatisfiable"
        ]

        combined_output = (stdout + " " + stderr).lower()
        for indicator in proof_indicators:
            if indicator in combined_output:
                self.logger.debug(f"发现证明相关输出: {indicator}")
                return True

        # 如果返回码不是0且没有找到正常的逻辑输出，可能是语法错误
        if return_code != 0 and stderr:
            self.logger.debug(f"返回码非0且有错误输出，可能是语法错误")
            return False

        # 默认认为语法正确
        return True
    
    def extract_error_message(self, stdout: str, stderr: str) -> str:
        """
        从eprover输出中提取错误信息

        Args:
            stdout (str): 标准输出
            stderr (str): 错误输出

        Returns:
            str: 提取的错误信息
        """
        error_lines = []

        # 从stderr提取错误（完整保留）
        if stderr:
            for line in stderr.split('\n'):
                line = line.strip()
                if line:  # 保留所有非空行
                    error_lines.append(line)

        # 从stdout提取错误
        if stdout:
            for line in stdout.split('\n'):
                line = line.strip()
                if line and any(keyword in line.lower() for keyword in
                              ['error', 'fatal', 'syntax', 'parse', 'unexpected', 'formula has']):
                    error_lines.append(line)

        if error_lines:
            return '\n'.join(error_lines)
        else:
            # 如果没有找到具体错误，返回完整输出
            return f"标准输出:\n{stdout}\n\n错误输出:\n{stderr}"
    
    def is_available(self) -> bool:
        """检查eprover是否可用"""
        return self._check_eprover_availability()
    
    def get_version(self) -> Optional[str]:
        """获取eprover版本信息"""
        try:
            result = subprocess.run(
                [self.eprover_path, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception:
            return None

    def can_determine_problem(self, tptp_content: str, filename: str = "temp.p") -> Tuple[bool, str]:
        """
        检查eprover是否能够正常判定问题（区分语法错误和逻辑判定）

        Args:
            tptp_content (str): TPTP内容
            filename (str): 文件名

        Returns:
            Tuple[bool, str]: (是否能判定, SZS状态或错误类型)
        """
        is_valid, stdout, stderr = self.validate_tptp_content(tptp_content, filename)

        if not is_valid:
            # 语法错误，需要修复
            return False, "SyntaxError"

        # 语法正确，检查是否有逻辑判定结果
        szs_status = self._extract_szs_status_internal(stdout)
        if szs_status and szs_status != "Unknown":
            # 有明确的逻辑判定结果
            return True, szs_status

        # 语法正确但没有明确的判定结果
        return True, "NoResult"

    def _extract_szs_status_internal(self, eprover_output: str) -> Optional[str]:
        """
        从eprover输出中提取SZS状态

        Args:
            eprover_output (str): eprover输出

        Returns:
            Optional[str]: SZS状态，如果没有找到返回None
        """
        import re

        # 查找 "# SZS status XXX" 模式
        szs_pattern = r'#\s*SZS\s+status\s+(\w+)'
        match = re.search(szs_pattern, eprover_output, re.IGNORECASE)

        if match:
            return match.group(1)

        return None
