"""
完整的TPTP双向转换工作流程
整合第一步（TPTP到自然语言）和第二步（自然语言到TPTP）
"""

import os
import sys
from pathlib import Path
from ai_client import AIClient
from tptp_processor import TPTPProcessor
from nl_to_tptp_processor import NLToTPTPProcessor
from eprover_validator import EProverValidator
from config import Config


def check_prerequisites(ai_provider_override=None):
    """检查运行前置条件"""
    print("检查运行前置条件...")
    print("-" * 40)

    issues = []

    # 检查API密钥（传递模型提供商覆盖参数）
    config_errors = Config.validate_config(ai_provider_override)
    if config_errors:
        issues.extend(config_errors)
    
    # 检查eprover
    nl_config = Config.get_nl_to_tptp_config()
    eprover_validator = EProverValidator(eprover_path=nl_config["eprover_path"])
    if not eprover_validator.is_available():
        issues.append("EProver不可用，请安装eprover")
    else:
        version = eprover_validator.get_version()
        print(f"✓ EProver可用: {version}")
    
    # 检查目录结构
    required_dirs = ["./tptp", "./tptp/Axioms"]
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            issues.append(f"目录不存在: {dir_path}")
    
    # 检查TPTP文件
    tptp_files = list(Path("./tptp").glob("*.p"))
    if not tptp_files:
        issues.append("tptp目录中没有.p文件")
    else:
        print(f"✓ 找到 {len(tptp_files)} 个TPTP文件")
    
    if issues:
        print("\n❌ 发现以下问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✓ 所有前置条件满足")
        return True


def run_step1(ai_provider_override=None):
    """执行第一步：TPTP到自然语言转换"""
    print("\n" + "=" * 60)
    print("第一步：TPTP到自然语言转换")
    print("=" * 60)

    try:
        # 获取配置
        ai_config = Config.get_ai_config()
        tptp_config = Config.get_tptp_config()

        # 如果有模型提供商覆盖，应用覆盖
        if ai_provider_override is not None:
            original_provider = os.environ.get("AI_PROVIDER", "openai")
            os.environ["AI_PROVIDER"] = ai_provider_override
            ai_config = Config.get_ai_config()
            # 恢复原始环境变量
            os.environ["AI_PROVIDER"] = original_provider

        # 初始化AI客户端
        ai_client = AIClient.create_from_config(ai_config)
        print(f"✓ 使用模型: {ai_config['provider'].upper()} - {ai_config['model']}")
        tptp_processor = TPTPProcessor(
            tptp_dir=tptp_config["tptp_dir"],
            nl_dir=tptp_config["nl_dir"],
            chatgpt_client=ai_client
        )
        
        # 获取统计信息
        stats = tptp_processor.get_processing_stats()
        print(f"TPTP文件数量: {stats['tptp_files']}")
        print(f"公理文件数量: {stats['axiom_files']}")
        
        # 处理文件
        print("\n开始处理TPTP文件...")
        results = tptp_processor.process_all_tptp_files()
        
        # 统计结果
        successful = sum(1 for success in results.values() if success)
        failed = len(results) - successful
        
        print(f"\n第一步完成:")
        print(f"  成功处理: {successful} 个文件")
        print(f"  处理失败: {failed} 个文件")
        
        if failed > 0:
            print("  失败的文件:")
            for filename, success in results.items():
                if not success:
                    print(f"    - {filename}")
        
        return successful > 0
        
    except Exception as e:
        print(f"第一步执行出错: {e}")
        return False


def run_step2(ai_provider_override=None, enable_preprocessing_override=None, max_fix_attempts_override=None):
    """执行第二步：自然语言到TPTP转换"""
    print("\n" + "=" * 60)
    print("第二步：自然语言到TPTP转换")
    print("=" * 60)

    try:
        # 获取配置
        ai_config = Config.get_ai_config()
        nl_config = Config.get_nl_to_tptp_config()

        # 如果有参数覆盖，应用覆盖
        if ai_provider_override is not None:
            original_provider = os.environ.get("AI_PROVIDER", "openai")
            os.environ["AI_PROVIDER"] = ai_provider_override
            ai_config = Config.get_ai_config()
            # 恢复原始环境变量
            os.environ["AI_PROVIDER"] = original_provider

        if enable_preprocessing_override is not None:
            nl_config["enable_nl_preprocessing"] = enable_preprocessing_override

        if max_fix_attempts_override is not None:
            nl_config["max_fix_attempts"] = max_fix_attempts_override

        # 初始化组件
        ai_client = AIClient.create_from_config(ai_config)
        print(f"✓ 使用模型: {ai_config['provider'].upper()} - {ai_config['model']}")
        eprover_validator = EProverValidator(eprover_path=nl_config["eprover_path"])
        
        nl_processor = NLToTPTPProcessor(
            nl_dir=nl_config["nl_dir"],
            transform_tptp_dir=nl_config["transform_tptp_dir"],
            result_dir=nl_config["result_dir"],
            chatgpt_client=ai_client,
            eprover_validator=eprover_validator,
            max_fix_attempts=nl_config["max_fix_attempts"],
            enable_nl_preprocessing=nl_config["enable_nl_preprocessing"],
            enable_validation_classification=nl_config["enable_validation_classification"],
            preprocessed_nl_dir=nl_config.get("preprocessed_nl_dir"),
            fix_steps_dir=nl_config.get("fix_steps_dir")
        )
        
        # 获取统计信息
        stats = nl_processor.get_processing_stats()
        print(f"自然语言文件数量: {stats['nl_files']}")
        
        if stats['nl_files'] == 0:
            print("❌ 没有自然语言文件可处理")
            return False
        
        # 处理文件
        print("\n开始处理自然语言文件...")
        results = nl_processor.process_all_nl_files()
        
        # 统计结果
        successful = sum(1 for success in results.values() if success)
        failed = len(results) - successful
        
        print(f"\n第二步完成:")
        print(f"  成功处理: {successful} 个文件")
        print(f"  处理失败: {failed} 个文件")
        
        if failed > 0:
            print("  失败的文件:")
            for filename, success in results.items():
                if not success:
                    print(f"    - {filename}")
        
        return successful > 0
        
    except Exception as e:
        print(f"第二步执行出错: {e}")
        return False


def compare_results():
    """比较原始TPTP和转换后的TPTP"""
    print("\n" + "=" * 60)
    print("结果比较")
    print("=" * 60)
    
    original_dir = Path("./tptp")
    transform_dir = Path("./transform_tptp")
    result_dir = Path("./result")
    
    if not transform_dir.exists():
        print("❌ 转换目录不存在")
        return
    
    original_files = {f.stem: f for f in original_dir.glob("*.p")}
    transform_files = {f.stem: f for f in transform_dir.glob("*.p")}
    
    print("文件对比:")
    for name in original_files:
        if name in transform_files:
            print(f"  ✓ {name}.p: 原始 → 自然语言 → TPTP")
            
            # 可以在这里添加更详细的比较逻辑
            original_size = original_files[name].stat().st_size
            transform_size = transform_files[name].stat().st_size
            print(f"    原始文件大小: {original_size} 字节")
            print(f"    转换文件大小: {transform_size} 字节")
        else:
            print(f"  ✗ {name}.p: 转换失败")
    
    # 显示目录结构
    print(f"\n目录结构:")
    print(f"  ./tptp/           - 原始TPTP文件")
    print(f"  ./nl/             - 自然语言文件")
    print(f"  ./transform_tptp/ - 转换后TPTP文件")
    print(f"  ./result/         - 一致性判定结果文件")


def main(ai_provider_override=None, enable_preprocessing_override=None, max_fix_attempts_override=None):
    """主函数 - 执行完整的双向转换工作流程"""

    print("TPTP双向转换完整工作流程")
    print("=" * 60)
    print("第一步: TPTP → 自然语言")
    print("第二步: 自然语言 → TPTP")
    print("=" * 60)

    # 显示配置信息
    if ai_provider_override:
        print(f"✓ AI模型提供商: {ai_provider_override.upper()}")
    if enable_preprocessing_override is not None:
        print(f"✓ 自然语言预处理: {'启用' if enable_preprocessing_override else '禁用'}")
    if max_fix_attempts_override is not None:
        print(f"✓ 最大修复尝试次数: {max_fix_attempts_override}")
    if ai_provider_override or enable_preprocessing_override is not None or max_fix_attempts_override is not None:
        print("=" * 60)
    
    # 检查前置条件
    if not check_prerequisites(ai_provider_override):
        print("\n❌ 前置条件不满足，请解决上述问题后重试")
        return
    
    # 询问用户是否继续
    response = input("\n是否继续执行完整工作流程？(y/n): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    try:
        # 执行第一步
        step1_success = run_step1(ai_provider_override)

        if not step1_success:
            print("\n❌ 第一步失败，停止执行")
            return

        # 询问是否继续第二步
        response = input("\n第一步完成。是否继续执行第二步？(y/n): ")
        if response.lower() != 'y':
            print("工作流程在第一步后停止")
            return

        # 执行第二步
        step2_success = run_step2(ai_provider_override, enable_preprocessing_override, max_fix_attempts_override)
        
        if step2_success:
            print("\n🎉 完整工作流程执行成功！")
            compare_results()
        else:
            print("\n❌ 第二步失败")
        
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
    except Exception as e:
        print(f"\n工作流程执行出错: {e}")
        import traceback
        traceback.print_exc()


def show_help():
    """显示帮助信息"""
    help_text = """
TPTP双向转换完整工作流程

这个脚本整合了两个步骤：
1. 第一步：TPTP → 自然语言转换
2. 第二步：自然语言 → TPTP转换

前置条件：
- 安装eprover并在PATH中可访问
- 设置有效的OpenAI API密钥
- tptp目录中存在.p文件

用法：
    python complete_workflow.py [选项]

选项：
    --help, -h                  显示帮助信息
    --model-provider NAME       设置AI模型提供商（openai/doubao/deepseek）
    --enable-preprocessing      启用自然语言预处理
    --max-fix-attempts N        设置最大修复尝试次数

示例：
    python complete_workflow.py                                    # 使用默认配置
    python complete_workflow.py --model-provider deepseek          # 使用DeepSeek模型
    python complete_workflow.py --model-provider doubao --enable-preprocessing --max-fix-attempts 3

目录结构：
    ./tptp/           # 输入：原始TPTP文件
    ./tptp/Axioms/    # 输入：公理文件
    ./nl/             # 中间：自然语言文件
    ./transform_tptp/ # 输出：转换后TPTP文件

单独执行步骤：
    python tptp_to_nl.py     # 仅执行第一步
    python nl_to_tptp.py     # 仅执行第二步
"""
    print(help_text)


if __name__ == "__main__":
    # 检查命令行参数
    ai_provider = None
    enable_preprocessing = None
    max_fix_attempts = None

    i = 1
    while i < len(sys.argv):
        arg = sys.argv[i]

        if arg in ["--help", "-h"]:
            show_help()
            sys.exit(0)
        elif arg == "--model-provider":
            if i + 1 < len(sys.argv):
                provider = sys.argv[i + 1].lower()
                if provider in ["openai", "doubao", "deepseek"]:
                    ai_provider = provider
                    print(f"✓ 设置AI模型提供商: {provider.upper()}")
                    i += 1  # 跳过下一个参数
                else:
                    print(f"错误：不支持的模型提供商 '{provider}'，支持: openai, doubao, deepseek")
                    sys.exit(1)
            else:
                print(f"错误：--model-provider 需要一个参数")
                sys.exit(1)
        elif arg == "--enable-preprocessing":
            enable_preprocessing = True
            print("✓ 启用自然语言预处理")
        elif arg == "--max-fix-attempts":
            if i + 1 < len(sys.argv):
                try:
                    max_fix_attempts = int(sys.argv[i + 1])
                    print(f"✓ 设置最大修复尝试次数: {max_fix_attempts}")
                    i += 1  # 跳过下一个参数
                except ValueError:
                    print(f"错误：--max-fix-attempts 需要一个整数参数")
                    sys.exit(1)
            else:
                print(f"错误：--max-fix-attempts 需要一个参数")
                sys.exit(1)
        else:
            print(f"未知选项: {arg}")
            print("使用 --help 查看帮助信息")
            sys.exit(1)

        i += 1

    # 调用main函数，传递参数
    main(ai_provider_override=ai_provider,
         enable_preprocessing_override=enable_preprocessing,
         max_fix_attempts_override=max_fix_attempts)
