# TPTP双向转换系统

一个基于多种AI模型的TPTP格式与自然语言双向转换工具，支持OpenAI GPT、字节跳动Doubao和DeepSeek R1模型。

## 🌟 主要特性

- **🔄 双向转换**：TPTP ↔ 自然语言
- **🤖 多模型支持**：OpenAI、Doubao、DeepSeek R1
- **🧠 智能预处理**：自动简化和去歧义自然语言
- **🔧 自动修复**：GPT智能修复TPTP语法错误
- **✅ 验证分类**：自动分类验证结果
- **📊 质量监控**：详细的统计和分析功能

## 🚀 快速开始

### 1. 自动化设置
```bash
# 运行自动化设置脚本
chmod +x setup.sh
./setup.sh
```

### 2. 手动设置API密钥
```bash
# OpenAI模型
export OPENAI_API_KEY="your-openai-api-key"

# 字节跳动模型（Doubao/DeepSeek）
export ARK_API_KEY="your-bytedance-api-key"
```

### 3. 一键转换
```bash
# 使用DeepSeek R1模型（推荐）
python complete_workflow.py --model-provider deepseek

# 使用OpenAI模型
python complete_workflow.py --model-provider openai

# 启用高级功能
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5
```

## 📋 详细文档

- 📖 **[快速入门指南](QUICK_START.md)** - 5分钟上手指南
- 📚 **[完整操作说明](OPERATION_GUIDE.md)** - 详细的使用说明
- 🔧 **[模型切换指南](MODEL_SWITCHING_GUIDE.md)** - AI模型切换详细说明

## 🛠️ 系统架构

### 核心组件
- **AIClient** - 统一的AI模型接口
- **TPTPProcessor** - TPTP到自然语言转换
- **NLToTPTPProcessor** - 自然语言到TPTP转换
- **EProverValidator** - TPTP语法验证
- **Config** - 统一配置管理

### 工作流程
```
TPTP文件 → AI转换 → 自然语言 → AI转换 → TPTP文件
    ↓           ↓           ↓           ↓
  解析包含    智能预处理    语法验证    错误修复
```

## 🤖 支持的AI模型

| 模型 | 提供商 | 特点 | 适用场景 |
|------|--------|------|----------|
| **GPT-3.5/4** | OpenAI | 综合能力强 | 英文内容、快速测试 |
| **Doubao** | 字节跳动 | 中文优化 | 中文内容、成本敏感 |
| **DeepSeek R1** | 字节跳动 | 推理优化 | 复杂逻辑、数学推理 |

## 📁 目录结构

```
NL2TPTP/
├── tptp/              # 输入：原始TPTP文件
├── nl/                # 中间：自然语言文件
├── preprocessed_nl/   # 预处理后的自然语言文件
├── transform_tptp/    # 输出：转换后TPTP文件
├── fix_steps/         # TPTP修复步骤和文件
├── result/            # 结果：验证和比较结果
├── prover/            # EProver工具
├── config.py          # 配置文件
├── complete_workflow.py    # 完整工作流程
├── tptp_to_nl.py      # TPTP→自然语言
├── nl_to_tptp.py      # 自然语言→TPTP
└── setup.sh           # 自动化设置脚本
```

## 🔧 使用方法

### 完整工作流程
```bash
# 基本使用
python complete_workflow.py

# 指定AI模型
python complete_workflow.py --model-provider deepseek

# 启用所有功能
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5
```

### 分步执行
```bash
# 第一步：TPTP → 自然语言
python tptp_to_nl.py --model-provider openai

# 第二步：自然语言 → TPTP
python nl_to_tptp.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 3
```

### 查看统计信息
```bash
python tptp_to_nl.py --stats
python nl_to_tptp.py --stats
```

## ⚙️ 高级功能

### 自然语言预处理
自动简化和去歧义自然语言描述：
```bash
python nl_to_tptp.py --enable-preprocessing
```

### 智能错误修复
GPT自动修复TPTP语法错误：
```bash
python nl_to_tptp.py --max-fix-attempts 5
```

### 验证结果分类
自动分类验证结果为VALID/INVALID，结果保存在`result/`目录。

## 📊 结果分析

### 验证结果文件
- `result/validation_summary.txt` - 验证结果汇总
- `result/valid_problems.txt` - 验证通过的问题
- `result/invalid_problems.txt` - 验证失败的问题
- `result/comparison_results.txt` - 一致性比较结果

### 查看结果
```bash
# 查看验证汇总
cat result/validation_summary.txt

# 统计成功率
grep "VALID" result/validation_summary.txt | wc -l
grep "INVALID" result/validation_summary.txt | wc -l
```

## 🔍 故障排除

### 常见问题
1. **API密钥错误**：检查环境变量设置
2. **EProver路径问题**：确保`prover/eprover`可执行
3. **文件编码问题**：确保使用UTF-8编码
4. **网络连接问题**：检查API端点可访问性

### 调试命令
```bash
# 检查配置
python -c "from config import Config; print(Config.get_ai_config())"

# 测试EProver
./prover/eprover --version

# 测试API连接
python tptp_to_nl.py --model-provider openai --stats
```

## 📈 性能优化

### 模型选择建议
- **快速测试**：OpenAI gpt-3.5-turbo
- **中文内容**：Doubao或DeepSeek R1
- **复杂逻辑**：DeepSeek R1或OpenAI gpt-4
- **批量处理**：考虑成本，选择Doubao或DeepSeek R1

### 参数调优
```bash
# 平衡质量和速度
python complete_workflow.py \
  --model-provider deepseek \
  --max-fix-attempts 3

# 最高质量（较慢）
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 10
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
```bash
git clone <repository>
cd NL2TPTP
./setup.sh
```

### 代码结构
- `ai_client.py` - AI模型统一接口
- `config.py` - 配置管理
- `*_processor.py` - 核心处理逻辑
- `eprover_validator.py` - TPTP验证

## 📄 许可证

本项目采用MIT许可证。

## 🙏 致谢

- OpenAI - GPT模型支持
- 字节跳动 - Doubao和DeepSeek模型支持
- EProver团队 - TPTP验证工具

---

**快速开始：** 阅读 [QUICK_START.md](QUICK_START.md) 获取5分钟入门指南！
