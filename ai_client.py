"""
统一的AI客户端，支持OpenAI和Doubao模型
"""

import os
import logging
from typing import List, Dict, Optional, Any
from openai import OpenAI


class AIClient:
    """统一的AI客户端，支持多种模型提供商"""
    
    def __init__(self, provider: str = "openai", api_key: str = None, model: str = None,
                 base_url: str = None, temperature: float = 0.7, max_tokens: int = None,
                 max_retries: int = 3, timeout: int = None):
        """
        初始化AI客户端

        Args:
            provider (str): 模型提供商 ("openai", "doubao" 或 "deepseek")
            api_key (str): API密钥
            model (str): 模型名称
            base_url (str): API基础URL
            temperature (float): 温度参数
            max_tokens (int): 最大token数
            max_retries (int): 最大重试次数
        """
        self.provider = provider.lower()
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.max_retries = max_retries
        self.timeout = timeout or (300 if provider.lower() == "deepseek" else 60)  # DeepSeek需要更长超时

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 初始化客户端
        self._init_client()
    
    def _init_client(self):
        """初始化具体的客户端"""
        try:
            if self.provider in ["doubao", "deepseek"]:
                # 初始化Doubao/DeepSeek客户端（使用相同的API端点）
                # 根据OpenAI文档，timeout应该使用httpx.Timeout对象或数字
                import httpx

                self.client = OpenAI(
                    base_url=self.base_url or "https://ark.cn-beijing.volces.com/api/v3",
                    api_key=self.api_key,
                    timeout=httpx.Timeout(self.timeout, read=self.timeout, write=self.timeout, connect=30.0)
                )
                provider_name = "Doubao" if self.provider == "doubao" else "DeepSeek R1"
                self.logger.info(f"✓ {provider_name}客户端初始化成功，模型: {self.model}，超时: {self.timeout}秒")

            elif self.provider == "openai":
                # 初始化OpenAI客户端
                import httpx

                client_kwargs = {
                    "api_key": self.api_key,
                    "timeout": httpx.Timeout(self.timeout, read=self.timeout, write=self.timeout, connect=30.0)
                }
                if self.base_url:
                    client_kwargs["base_url"] = self.base_url

                self.client = OpenAI(**client_kwargs)
                self.logger.info(f"✓ OpenAI客户端初始化成功，模型: {self.model}，超时: {self.timeout}秒")

            else:
                raise ValueError(f"不支持的模型提供商: {self.provider}")

        except Exception as e:
            self.logger.error(f"AI客户端初始化失败: {e}")
            raise
    
    def get_response_with_context(self, messages: List[Dict[str, str]], 
                                 temperature: Optional[float] = None,
                                 max_tokens: Optional[int] = None) -> Optional[str]:
        """
        获取AI响应（统一接口）
        
        Args:
            messages (List[Dict]): 消息列表
            temperature (float, optional): 温度参数
            max_tokens (int, optional): 最大token数
            
        Returns:
            Optional[str]: AI响应内容
        """
        try:
            # 使用传入的参数或默认参数
            temp = temperature if temperature is not None else self.temperature
            tokens = max_tokens if max_tokens is not None else self.max_tokens
            
            # 构建请求参数
            request_params = {
                "model": self.model,
                "messages": messages,
                "temperature": temp
            }
            
            if tokens:
                request_params["max_tokens"] = tokens
            
            # 重试机制
            for attempt in range(self.max_retries):
                try:
                    self.logger.info(f"正在调用 {self.provider.upper()} API (尝试 {attempt + 1}/{self.max_retries})")
                    if attempt == 0:
                        self.logger.debug(f"请求参数: {request_params}")

                    # 调用API
                    response = self.client.chat.completions.create(**request_params)

                    # 提取响应内容
                    if response.choices and len(response.choices) > 0:
                        content = response.choices[0].message.content
                        if content:
                            self.logger.info(f"✓ {self.provider.upper()} API调用成功，响应长度: {len(content)}")
                            return content

                    self.logger.warning(f"API响应为空 (尝试 {attempt + 1}/{self.max_retries})")

                except Exception as e:
                    self.logger.error(f"API调用失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")

                    # 如果是最后一次尝试，不再重试
                    if attempt == self.max_retries - 1:
                        break

                    # 等待一段时间后重试
                    import time
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            return None

        except Exception as e:
            self.logger.error(f"{self.provider.upper()} API调用失败: {e}")
            return None
    
    def get_completion(self, prompt: str, system_message: str = None,
                      temperature: Optional[float] = None,
                      max_tokens: Optional[int] = None) -> Optional[str]:
        """
        获取单次完成响应（便捷方法）
        
        Args:
            prompt (str): 用户提示
            system_message (str, optional): 系统消息
            temperature (float, optional): 温度参数
            max_tokens (int, optional): 最大token数
            
        Returns:
            Optional[str]: AI响应内容
        """
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": prompt})
        
        return self.get_response_with_context(messages, temperature, max_tokens)

    def get_response(self, prompt: str, system_message: str = None,
                    temperature: Optional[float] = None,
                    max_tokens: Optional[int] = None) -> Optional[str]:
        """
        获取响应（向后兼容方法）

        Args:
            prompt (str): 用户提示
            system_message (str, optional): 系统消息
            temperature (float, optional): 温度参数
            max_tokens (int, optional): 最大token数

        Returns:
            Optional[str]: AI响应内容
        """
        return self.get_completion(prompt, system_message, temperature, max_tokens)
    
    def is_available(self) -> bool:
        """检查AI客户端是否可用"""
        try:
            # 发送一个简单的测试请求
            test_response = self.get_completion("Hello", temperature=0.1)
            return test_response is not None
        except Exception as e:
            self.logger.error(f"AI客户端可用性检查失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": self.provider,
            "model": self.model,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> 'AIClient':
        """从配置字典创建AI客户端"""
        # 只提取支持的参数，避免传递不支持的参数
        supported_params = {
            "provider": config.get("provider", "openai"),
            "api_key": config.get("api_key"),
            "model": config.get("model"),
            "base_url": config.get("base_url"),
            "temperature": config.get("temperature", 0.7),
            "max_tokens": config.get("max_tokens"),
            "max_retries": config.get("max_retries", 3),
            "timeout": config.get("timeout")
        }

        # 过滤掉None值
        filtered_params = {k: v for k, v in supported_params.items() if v is not None}

        return cls(**filtered_params)


# 向后兼容的ChatGPTClient类
class ChatGPTClient(AIClient):
    """ChatGPT客户端（向后兼容）"""
    
    def __init__(self, **kwargs):
        # 如果没有指定provider，默认使用openai
        if "provider" not in kwargs:
            kwargs["provider"] = "openai"
        super().__init__(**kwargs)


def create_ai_client_from_config(config: Dict[str, Any]) -> AIClient:
    """
    从配置创建AI客户端的工厂函数
    
    Args:
        config (Dict): 配置字典
        
    Returns:
        AIClient: AI客户端实例
    """
    return AIClient.create_from_config(config)
