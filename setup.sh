#!/bin/bash

# TPTP双向转换系统自动化设置脚本
# 版本：2.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  TPTP双向转换系统 - 自动化设置脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查Python版本
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
    print_success "Python版本: $python_version"
    
    # 检查版本是否满足要求
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_success "Python版本满足要求"
    else
        print_error "Python版本过低，需要3.8+，当前版本: $python_version"
        exit 1
    fi
}

# 检查并安装Python依赖
install_dependencies() {
    print_info "检查Python依赖..."
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        print_warning "pip3未找到，尝试使用python3 -m pip"
        PIP_CMD="python3 -m pip"
    else
        PIP_CMD="pip3"
    fi
    
    # 安装依赖
    print_info "安装Python依赖包..."
    $PIP_CMD install --user openai 2>/dev/null || true
    
    print_success "Python依赖安装完成"
}

# 检查EProver
check_eprover() {
    print_info "检查EProver..."
    
    if [ -f "prover/eprover" ]; then
        if [ -x "prover/eprover" ]; then
            version=$(./prover/eprover --version 2>&1 | head -1 || echo "未知版本")
            print_success "EProver可用: $version"
        else
            print_warning "EProver文件存在但不可执行，修复权限..."
            chmod +x prover/eprover
            print_success "EProver权限已修复"
        fi
    else
        print_error "EProver未找到: prover/eprover"
        print_info "请确保EProver文件位于prover/目录下"
        exit 1
    fi
}

# 创建目录结构
setup_directories() {
    print_info "创建目录结构..."
    
    directories=("tptp" "nl" "transform_tptp" "result" "preprocessed_nl" "fix_steps")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "创建目录: $dir/"
        else
            print_info "目录已存在: $dir/"
        fi
    done
}

# 配置API密钥
setup_api_keys() {
    print_info "配置AI模型API密钥..."
    
    echo ""
    echo "请选择要配置的AI模型："
    echo "1) OpenAI GPT"
    echo "2) 字节跳动 Doubao/DeepSeek"
    echo "3) 跳过配置（稍后手动设置）"
    
    read -p "请输入选择 (1/2/3): " api_choice
    
    case $api_choice in
        1)
            echo ""
            read -p "请输入OpenAI API密钥: " openai_key
            if [ ! -z "$openai_key" ]; then
                export OPENAI_API_KEY="$openai_key"
                print_success "OpenAI API密钥已配置"
            fi
            ;;
        2)
            echo ""
            read -p "请输入字节跳动API密钥 (ARK_API_KEY): " ark_key
            if [ ! -z "$ark_key" ]; then
                export ARK_API_KEY="$ark_key"
                print_success "字节跳动API密钥已配置"
            fi
            ;;
        3)
            print_warning "跳过API密钥配置"
            print_info "稍后请手动设置："
            echo "  export OPENAI_API_KEY=\"your-openai-key\""
            echo "  export ARK_API_KEY=\"your-bytedance-key\""
            ;;
        *)
            print_warning "无效选择，跳过API密钥配置"
            ;;
    esac
}

# 创建示例文件
create_sample_files() {
    print_info "创建示例TPTP文件..."
    
    if [ ! -f "tptp/example.p" ]; then
        cat > tptp/example.p << 'EOF'
% Example TPTP problem
% Simple logic problem about humans and mortality

fof(human_mortal, axiom, ![X]: (human(X) => mortal(X))).
fof(socrates_human, axiom, human(socrates)).
fof(prove_mortal, conjecture, mortal(socrates)).
EOF
        print_success "创建示例文件: tptp/example.p"
    else
        print_info "示例文件已存在: tptp/example.p"
    fi
}

# 显示使用说明
show_usage() {
    print_info "设置完成！使用说明："
    echo ""
    echo "📋 快速开始："
    echo "  python complete_workflow.py --model-provider openai"
    echo "  python complete_workflow.py --model-provider deepseek"
    echo ""
    echo "📖 查看帮助："
    echo "  python complete_workflow.py --help"
    echo "  cat QUICK_START.md"
    echo "  cat OPERATION_GUIDE.md"
    echo ""
    echo "🔧 测试命令："
    echo "  python tptp_to_nl.py --stats"
    echo "  python nl_to_tptp.py --stats"
    echo ""
    
    if [ -z "$OPENAI_API_KEY" ] && [ -z "$ARK_API_KEY" ]; then
        print_warning "请设置API密钥后再使用："
        echo "  export OPENAI_API_KEY=\"your-openai-key\""
        echo "  export ARK_API_KEY=\"your-bytedance-key\""
    fi
}

# 主函数
main() {
    print_header
    
    # 检查当前目录
    if [ ! -f "config.py" ]; then
        print_error "请在TPTP双向转换系统根目录下运行此脚本"
        exit 1
    fi
    
    print_info "开始自动化设置..."
    echo ""
    
    # 执行设置步骤
    check_python
    echo ""
    
    install_dependencies
    echo ""
    
    check_eprover
    echo ""
    
    setup_directories
    echo ""
    
    setup_api_keys
    echo ""
    
    create_sample_files
    echo ""
    
    print_success "🎉 系统设置完成！"
    echo ""
    show_usage
}

# 运行主函数
main "$@"
