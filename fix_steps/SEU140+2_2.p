fof(antisymmetry_r2_hidden, axiom, (
  ! [A,B] : (in(B,A) => ~in(A,B))
)).

fof(antisymmetry_r2_xboole_0, axiom, (
  ! [A,B] : (proper_subset(A,B) => ~proper_subset(B,A))
)).

fof(commutativity_k2_xboole_0, axiom, (
  ! [A,B] : set_union2(A,B) = set_union2(B,A)
)).

fof(commutativity_k3_xboole_0, axiom, (
  ! [A,B] : set_intersection2(A,B) = set_intersection2(B,A)
)).

fof(d10_xboole_0, axiom, (
  ! [A,B] : (A = B <=> (subset(A,B) & subset(B,A)))
)).

fof(d1_xboole_0, axiom, (
  ! [A] : (A = empty_set <=> ! [B] : ~in(B,A))
)).

fof(d2_xboole_0, axiom, (
  ! [A,B,C] : (C = set_union2(A,B) <=> ! [D] : (in(D,C) <=> (in(D,A) | in(D,B))))
)).

fof(d3_tarski, axiom, (
  ! [A,B] : (subset(A,B) <=> ! [C] : (in(C,A) => in(C,B)))
)).

fof(d3_xboole_0, axiom, (
  ! [A,B,C] : (C = set_intersection2(A,B) <=> ! [D] : (in(D,C) <=> (in(D,A) & in(D,B))))
)).

fof(d4_xboole_0, axiom, (
  ! [A,B,C] : (C = set_difference(A,B) <=> ! [D] : (in(D,C) <=> (in(D,A) & ~in(D,B))))
)).

fof(d7_xboole_0, axiom, (
  ! [A,B] : (disjoint(A,B) <=> set_intersection2(A,B) = empty_set)
)).

fof(d8_xboole_0, axiom, (
  ! [A,B] : (proper_subset(A,B) <=> (subset(A,B) & A != B))
)).

fof(dt_k1_xboole_0, axiom, ( $true )).

fof(dt_k2_xboole_0, axiom, ( $true )).

fof(dt_k3_xboole_0, axiom, ( $true )).

fof(dt_k4_xboole_0, axiom, ( $true )).

fof(fc1_xboole_0, axiom, ( empty(empty_set) )).

fof(fc2_xboole_0, axiom, (
  ! [A,B] : (~empty(A) => ~empty(set_union2(A,B)))
)).

fof(fc3_xboole_0, axiom, (
  ! [A,B] : (~empty(A) => ~empty(set_union2(B,A)))
)).

fof(idempotence_k2_xboole_0, axiom, (
  ! [A] : set_union2(A,A) = A
)).

fof(idempotence_k3_xboole_0, axiom, (
  ! [A] : set_intersection2(A,A) = A
)).

fof(irreflexivity_r2_xboole_0, axiom, (
  ! [A] : ~proper_subset(A,A)
)).

fof(rc1_xboole_0, axiom, (
  ? [A] : empty(A)
)).

fof(rc2_xboole_0, axiom, (
  ? [A] : ~empty(A)
)).

fof(reflexivity_r1_tarski, axiom, (
  ! [A,B] : subset(A,A)
)).

fof(symmetry_r1_xboole_0, axiom, (
  ! [A,B] : (disjoint(A,B) => disjoint(B,A))
)).

fof(t1_boole, axiom, (
  ! [A] : set_union2(A,empty_set) = A
)).

fof(t2_boole, axiom, (
  ! [A] : set_intersection2(A,empty_set) = empty_set
)).

fof(t2_tarski, axiom, (
  ! [A,B] : (! [C] : (in(C,A) <=> in(C,B)) => A = B)
)).

fof(t3_boole, axiom, (
  ! [A] : set_difference(A,empty_set) = A
)).

fof(t4_boole, axiom, (
  ! [A] : set_difference(empty_set,A) = empty_set
)).

fof(t6_boole, axiom, (
  ! [A] : (empty(A) => A = empty_set)
)).

fof(t7_boole, axiom, (
  ! [A,B] : (in(A,B) => ~empty(B))
)).

fof(t8_boole, axiom, (
  ! [A,B] : ((empty(A) & A != B) => ~empty(B))
)).

fof(l32_xboole_1, lemma, (
  ! [A,B] : (set_difference(A,B) = empty_set <=> subset(A,B))
)).

fof(t12_xboole_1, lemma, (
  ! [A,B] : (subset(A,B) => set_union2(A,B) = B)
)).

fof(t17_xboole_1, lemma, (
  ! [A,B] : subset(set_intersection2(A,B),A)
)).

fof(t19_xboole_1, lemma, (
  ! [A,B,C] : ((subset(A,B) & subset(A,C)) => subset(A,set_intersection2(B,C)))
)).

fof(t1_xboole_1, lemma, (
  ! [A,B,C] : ((subset(A,B) & subset(B,C)) => subset(A,C))
)).

fof(t26_xboole_1, lemma, (
  ! [A,B,C] : (subset(A,B) => subset(set_intersection2(A,C),set_intersection2(B,C)))
)).

fof(t28_xboole_1, lemma, (
  ! [A,B] : (subset(A,B) => set_intersection2(A,B) = A)
)).

fof(t2_xboole_1, lemma, (
  ! [A] : subset(empty_set,A)
)).

fof(t33_xboole_1, lemma, (
  ! [A,B,C] : (subset(A,B) => subset(set_difference(A,C),set_difference(B,C)))
)).

fof(t36_xboole_1, lemma, (
  ! [A,B] : subset(set_difference(A,B),A)
)).

fof(t63_xboole_1, conjecture, (
  ! [A,B,C] : ((subset(A,B) & disjoint(B,C)) => disjoint(A,C))
)).