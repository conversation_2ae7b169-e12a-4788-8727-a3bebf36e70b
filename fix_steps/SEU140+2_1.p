% File: SEU140+2.nl
% Formalization of set theory axioms and properties in TPTP format

fof(ax_symmetry_element_relation, axiom, ! [A,B] : ( ( in(A,B) ) => ~ ( in(B,A) ) ) ).
fof(ax_symmetry_subset_relation, axiom, ! [A,B] : ( ( subset(A,B) ) => ~ ( subset(B,A) ) ) ).
fof(ax_commutative_union, axiom, ! [A,B] : ( equal(union(A,B), union(B,A)) ) ).
fof(ax_commutative_intersection, axiom, ! [A,B] : ( equal(intersection(A,B), intersection(B,A)) ) ).
fof(ax_empty_set_definition, axiom, ! [A] : ( ( equal(A, emptyset) ) <=> ( ! [x] : ~ ( in(x,A) ) ) ) ).
fof(ax_union_definition, axiom, ! [A,B,C] : ( ( equal(C, union(A,B)) ) <=> ( ! [D] : ( in(D,C) <=> ( in(D,A) | in(D,B) ) ) ) ) ).
fof(ax_subset_definition, axiom, ! [A,B] : ( ( subseteq(A,B) ) <=> ( ! [C] : ( in(C,A) => in(C,B) ) ) ) ).
fof(ax_intersection_definition, axiom, ! [A,B,C] : ( ( equal(C, intersection(A,B)) ) <=> ( ! [D] : ( in(D,C) <=> ( in(D,A) & in(D,B) ) ) ) ) ).
fof(ax_difference_definition, axiom, ! [A,B,C] : ( ( equal(C, difference(A,B)) ) <=> ( ! [D] : ( in(D,C) <=> ( in(D,A) & ~ in(D,B) ) ) ) ) ).
fof(ax_disjoint_definition, axiom, ! [A,B] : ( ( equal(intersection(A,B), emptyset) ) <=> ( ! [D] : ( in(D,A) => ~ in(D,B) ) ) ) ).
fof(ax_strict_subset_definition, axiom, ! [A,B] : ( ( strict_subset(A,B) ) <=> ( subseteq(A,B) & ~ equal(A,B) ) ) ).
fof(ax_exists_empty, axiom, ? [A] : ( equal(A, emptyset) ) ).
fof(ax_nonempty, axiom, ! [A] : ( ( ~ equal(A, emptyset) ) ) => ( ? [x] : in(x,A) ) ).
fof(ax_idempotent_union, axiom, ! [A] : ( equal(union(A,A), A) ) ).
fof(ax_idempotent_intersection, axiom, ! [A] : ( equal(intersection(A,A), A) ) ).
fof(ax_nonreflexive_strict_subset, axiom, ! [A] : ~ (strict_subset(A,A)) ).
fof(ax_unique_empty, axiom, ! [A] : ( equal(A, emptyset) ) => ( (A = emptyset) & ( ! [X] : ( in(X,A) ) ) ) ).
fof(ax_empty_nonempty_relation, axiom, ! [A] : ( ( ~ equal(A, emptyset) ) ) => ( ? [X] : in(X,A) ) ).
fof(ax_equality_definition, axiom, ! [A,B] : ( ( equal(A,B) ) <=> ( subseteq(A,B) & subseteq(B,A) ) ) ).
fof(ax_union_def, axiom, ! [A,B] : ( equal(union(A,B), {X : in(X,A) | in(X,B)}) ) ).
fof(ax_subset_def, axiom, ! [A,B] : ( ( subseteq(A,B) ) <=> ( ! [X] : ( in(X,A) => in(X,B) ) ) ) ).
fof(ax_intersection_def, axiom, ! [A,B] : ( equal(intersection(A,B), {X : in(X,A) & in(X,B)}) ) ).
fof(ax_difference_def, axiom, ! [A,B] : ( equal(difference(A,B), {X : in(X,A) & ~ in(X,B)}) ) ).
fof(ax_disjoint_def, axiom, ! [A,B] : ( equal(intersection(A,B), emptyset) <=> ( ! [X] : ( in(X,A) => ~ in(X,B) ) ) ) ).
fof(ax_strict_subset_def, axiom, ! [A,B] : ( ( strict_subset(A,B) ) <=> ( subseteq(A,B) & ~ equal(A,B) ) ) ).
% Additional properties and theorems to be proved
fof(theorem_subsets_transitive, conjecture, ! [A,B,C] : ( ( subseteq(A,B) & subseteq(B,C) ) => subseteq(A,C) ) ).
fof(theorem_union_commutative, conjecture, ! [A,B] : equal(union(A,B), union(B,A)) ).
fof(theorem_intersection_commutative, conjecture, ! [A,B] : equal(intersection(A,B), intersection(B,A)) ).
fof(theorem_subset_antisymmetric, conjecture, ! [A,B] : ( ( subseteq(A,B) & subseteq(B,A) ) => equal(A,B) ) ).
fof(theorem_empty_subset_of_all, conjecture, ! [A] : subseteq(emptyset,A) ).
fof(theorem_empty_set_unique, conjecture, ! [A] : ( equal(A, emptyset) ) => ( (A = emptyset) ) ).

% Note: The above formalization uses predicate symbols:
% in(X,A): element X belongs to set A
% subseteq(A,B): A is subset of B
% equal(A,B): A equals B
% union(A,B): union of A and B
% intersection(A,B): intersection of A and B
% difference(A,B): difference of A and B
% emptyset: the empty set
% strict_subset(A,B): A is a strict subset of B

% Definitions of predicates:
% For clarity, these are assumed to be defined or understood in the context of the formalization.

% End of formalization