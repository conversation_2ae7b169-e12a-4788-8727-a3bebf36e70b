% File: SET_PROBLEM+1.pl
% This TPTP file formalizes the subset relation and its transitivity in set theory.

fof(subset_definition, axiom, ! [X, Y] : (subset(X, Y) <=> ! [Z] : (in(Z, X) => in(Z, Y)))).

fof(equality_definition, axiom, ! [X, Y] : (X = Y <=> (subset(X, Y) & subset(Y, X)))).

fof(union_definition, axiom, ! [X, Y, Z] : (in(Z, union(X, Y)) <=> (in(Z, X) | in(Z, Y)))).

fof(intersection_definition, axiom, ! [X, Y, Z] : (in(Z, intersection(X, Y)) <=> (in(Z, X) & in(Z, Y)))).

fof(difference_definition, axiom, ! [X, Y, Z] : (in(Z, difference(X, Y)) <=> (in(Z, X) & ~in(Z, Y)))).

fof(empty_set_definition, axiom, ! [Z] : (~in(Z, empty_set))). 

fof(universal_set_definition, axiom, ! [Z] : (in(Z, universal_set))). 

fof(complement_definition, axiom, ! [X, Y] : (in(Y, complement(X)) <=> ~in(Y, X))).

% Hypotheses: subset relations
fof(hyp_sub_a_b, hypothesis, subset(a, b)).
fof(hyp_sub_b_c, hypothesis, subset(b, c)).

% Conjecture: subset transitivity
fof(conjecture_subset_transitive, conjecture, ! [Z] : ((subset(a, b) & subset(b, c)) => subset(a, c))).

% Note: The variables a, b, c are assumed to be sets; their types are implicit in the formalization.