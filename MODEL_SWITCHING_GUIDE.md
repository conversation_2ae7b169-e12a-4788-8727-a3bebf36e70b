# AI模型切换使用指南

## 🎯 功能概述

TPTP双向转换系统现在支持多种AI模型提供商，可以在OpenAI和字节跳动Doubao模型之间灵活切换。

## 🔧 支持的模型

### OpenAI模型
- **默认模型**: `gpt-3.5-turbo`
- **可选模型**: `gpt-4`, `gpt-4-turbo`
- **API端点**: OpenAI官方API

### Doubao模型
- **默认模型**: `doubao-seed-1-6-250615`
- **API端点**: `https://ark.cn-beijing.volces.com/api/v3`
- **提供商**: 字节跳动

### DeepSeek R1模型
- **默认模型**: `deepseek-r1-250528`
- **API端点**: `https://ark.cn-beijing.volces.com/api/v3`
- **提供商**: 字节跳动（部署DeepSeek R1）

## 🚀 使用方法

### 1. 命令行参数切换（推荐）

#### 使用OpenAI模型
```bash
python nl_to_tptp.py --model-provider openai
```

#### 使用Doubao模型
```bash
python nl_to_tptp.py --model-provider doubao
```

#### 使用DeepSeek R1模型
```bash
python nl_to_tptp.py --model-provider deepseek
```

#### 组合使用
```bash
# 使用Doubao模型，启用预处理，设置修复次数
python nl_to_tptp.py --model-provider doubao --enable-preprocessing --max-fix-attempts 3

# 使用OpenAI模型，设置修复次数
python nl_to_tptp.py --model-provider openai --max-fix-attempts 5

# 使用DeepSeek R1模型，启用预处理
python nl_to_tptp.py --model-provider deepseek --enable-preprocessing
```

### 2. 环境变量配置

#### 设置模型提供商
```bash
export AI_PROVIDER=doubao
python nl_to_tptp.py

# 或使用DeepSeek R1
export AI_PROVIDER=deepseek
python nl_to_tptp.py
```

#### OpenAI配置
```bash
export AI_PROVIDER=openai
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_MODEL="gpt-4"
```

#### Doubao配置
```bash
export AI_PROVIDER=doubao
export ARK_API_KEY="your-doubao-api-key"
export DOUBAO_MODEL="doubao-seed-1-6-250615"
export DOUBAO_BASE_URL="https://ark.cn-beijing.volces.com/api/v3"
```

#### DeepSeek R1配置
```bash
export AI_PROVIDER=deepseek
export ARK_API_KEY="your-deepseek-api-key"
export DEEPSEEK_MODEL="deepseek-r1-250528"
export DEEPSEEK_BASE_URL="https://ark.cn-beijing.volces.com/api/v3"
```

### 3. 配置文件修改

修改 `config.py` 中的默认配置：

```python
# 设置默认模型提供商
AI_PROVIDER = "doubao"  # 或 "openai"

# Doubao配置
DOUBAO_API_KEY = "your-doubao-api-key"
DOUBAO_MODEL = "doubao-seed-1-6-250615"
DOUBAO_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
```

## 🔑 API密钥配置

### OpenAI API密钥
```bash
export OPENAI_API_KEY="sk-your-openai-api-key"
```

### Doubao/DeepSeek API密钥
```bash
export ARK_API_KEY="your-api-key"  # Doubao和DeepSeek使用相同的密钥
```

## 📋 完整配置示例

### 示例1：使用Doubao模型进行完整转换
```bash
#!/bin/bash
# 设置Doubao配置
export AI_PROVIDER=doubao
export ARK_API_KEY="your-doubao-api-key"
export DOUBAO_MODEL="doubao-seed-1-6-250615"

# 运行转换
python complete_workflow.py
```

### 示例2：使用OpenAI模型进行单步转换
```bash
#!/bin/bash
# 设置OpenAI配置
export AI_PROVIDER=openai
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_MODEL="gpt-4"

# 运行第二步转换
python nl_to_tptp.py --enable-preprocessing --max-fix-attempts 5
```

### 示例3：命令行快速切换
```bash
# 使用Doubao模型
python nl_to_tptp.py --model-provider doubao

# 使用OpenAI模型
python nl_to_tptp.py --model-provider openai

# 使用DeepSeek R1模型
python nl_to_tptp.py --model-provider deepseek
```

## 🔍 验证配置

### 检查当前配置
```python
from config import Config

# 获取当前AI配置
ai_config = Config.get_ai_config()
print(f"当前提供商: {ai_config['provider']}")
print(f"当前模型: {ai_config['model']}")
print(f"API密钥: {ai_config['api_key'][:10]}...")
```

### 测试模型连接
```python
from ai_client import AIClient
from config import Config

# 创建客户端
ai_config = Config.get_ai_config()
client = AIClient.create_from_config(ai_config)

# 测试连接
response = client.get_completion("Hello")
print(f"连接测试: {'成功' if response else '失败'}")
```

## ⚙️ 高级配置

### 自定义模型参数
```python
# 在config.py中自定义参数
DEFAULT_TEMPERATURE = 0.7  # 温度参数
DEFAULT_MAX_TOKENS = None   # 最大token数
MAX_RETRIES = 3            # 最大重试次数
```

### 自定义API端点
```bash
# 自定义OpenAI端点
export OPENAI_BASE_URL="https://your-custom-endpoint.com/v1"

# 自定义Doubao端点
export DOUBAO_BASE_URL="https://your-custom-doubao-endpoint.com/api/v3"
```

## 🚨 注意事项

### 1. API密钥安全
- 不要在代码中硬编码API密钥
- 使用环境变量或安全的配置文件
- 不要将API密钥提交到版本控制系统

### 2. 模型兼容性
- 两种模型都使用相同的接口，无需修改提示词
- 不同模型的响应质量可能有差异
- 建议根据具体任务选择合适的模型

### 3. 网络配置
- 确保能够访问相应的API端点
- 检查防火墙和代理设置
- Doubao模型需要访问字节跳动的API服务

### 4. 成本考虑
- 不同模型的计费方式可能不同
- 建议根据预算选择合适的模型
- 可以通过设置max_tokens控制成本

## 🔧 故障排除

### 常见问题

#### 1. API密钥错误
```
错误: Authentication failed
解决: 检查API密钥是否正确设置
```

#### 2. 网络连接问题
```
错误: Request timed out
解决: 检查网络连接和API端点可访问性
```

#### 3. 模型不存在
```
错误: Model not found
解决: 检查模型名称是否正确
```

#### 4. 配置冲突
```
错误: Configuration conflict
解决: 清理环境变量，重新设置配置
```

### 调试方法

#### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 检查配置
```bash
python -c "from config import Config; print(Config.get_ai_config())"
```

## 📈 性能对比

| 特性 | OpenAI | Doubao | DeepSeek R1 |
|------|--------|--------|-------------|
| 响应速度 | 快 | 快 | 快 |
| 中文支持 | 好 | 优秀 | 优秀 |
| 逻辑推理 | 优秀 | 好 | 优秀 |
| 数学推理 | 好 | 好 | 优秀 |
| 成本 | 中等 | 较低 | 较低 |
| 可用性 | 全球 | 中国 | 中国 |

## 🎯 最佳实践

1. **开发阶段**: 使用gpt-3.5-turbo或doubao进行快速测试
2. **生产环境**: 根据质量要求选择gpt-4或doubao
3. **批量处理**: 考虑成本因素，选择性价比高的模型
4. **中文任务**: Doubao和DeepSeek R1在中文理解方面表现更好
5. **复杂逻辑**: OpenAI GPT-4和DeepSeek R1在复杂逻辑推理方面更强
6. **数学推理**: DeepSeek R1在数学和逻辑推理方面表现优异

## 🚀 快速开始

```bash
# 1. 设置API密钥
export ARK_API_KEY="your-api-key"

# 2. 使用DeepSeek R1模型运行转换
python nl_to_tptp.py --model-provider deepseek --enable-preprocessing

# 3. 查看结果
ls -la transform_tptp/ result/
```

现在您可以灵活地在三种AI模型之间切换，享受更好的转换体验！🎉

## 🆕 DeepSeek R1特色

DeepSeek R1是专门针对推理任务优化的模型，特别适合：
- **逻辑推理任务**：在TPTP格式转换中表现优异
- **数学证明**：对数学逻辑有更好的理解
- **中文支持**：对中文逻辑表达有很好的支持
- **成本效益**：提供高质量的推理能力，成本相对较低
