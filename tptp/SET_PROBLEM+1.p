%------------------------------------------------------------------------------
% File     : SET_PROBLEM+1 : Test problem with includes
% Domain   : Set Theory
% Problem  : Test problem using set theory axioms
% Version  : Test version
% English  : Prove that if A is a subset of B and B is a subset of C, 
%            then A is a subset of C (transitivity of subset relation)

% Status   : Theorem
% Syntax   : Number of formulae    :    3 (   2 unt;   0 def)
%            Number of atoms       :    5 (   0 equ)
%            Maximal formula atoms :    3 (   1 avg)
%            Number of connectives :    4 (   0   ~;   0   |;   2   &)
%                                         (   0 <=>;   2  =>;   0  <=;   0 <~>)
%            Maximal formula depth :    5 (   3 avg)
%            Maximal term depth    :    1 (   1 avg)
%            Number of predicates  :    1 (   1 usr;   0 prp; 2-2 aty)
%            Number of functors    :    0 (   0 usr;   0 con; --- aty)
%            Number of variables   :    3 (   3   !;   0   ?)
% SPC      : FOF_THM_RFO_NEQ

% Comments : This problem includes set theory axioms from external file
%------------------------------------------------------------------------------
%----Include set theory axioms
include(Axioms/SET001+0.ax).

%----Problem specific axioms
fof(premise1,hypothesis,
    subset(a,b) ).

fof(premise2,hypothesis,
    subset(b,c) ).

%----Goal to prove
fof(goal,conjecture,
    subset(a,c) ).

%------------------------------------------------------------------------------
