%------------------------------------------------------------------------------
% File     : AGT001+1 : TPTP v9.0.0. Released v2.7.0.
% Domain   : Agents
% Axioms   : Agent theory axioms
% Version  : [WV02] axioms.
% English  :

% Refs     : [WV02] <PERSON><PERSON><PERSON> (2002), Cooperation, Knowledge
% Source   : [WV02]
% Names    :

% Status   : Satisfiable
% Syntax   : Number of formulae    :    8 (   0 unt;   0 def)
%            Number of atoms       :   26 (   0 equ)
%            Maximal formula atoms :    5 (   3 avg)
%            Number of connectives :   18 (   0   ~;   0   |;   8   &)
%                                         (   0 <=>;  10  =>;   0  <=;   0 <~>)
%            Maximal formula depth :    7 (   5 avg)
%            Maximal term depth    :    2 (   1 avg)
%            Number of predicates  :    4 (   4 usr;   0 prp; 1-3 aty)
%            Number of functors    :    0 (   0 usr;   0 con; --- aty)
%            Number of variables   :   20 (  20   !;   0   ?)
% SPC      : FOF_SAT_RFO_NEQ

% Comments :
%------------------------------------------------------------------------------
%----Agent theory axioms
fof(agent_exists,axiom,
    ! [X] : 
      ( agent(X)
     => exists_agent(X) ) ).

fof(agent_action,axiom,
    ! [X,A] : 
      ( ( agent(X)
        & action(A) )
     => ( can_perform(X,A)
       => performs(X,A) ) ) ).

fof(agent_knowledge,axiom,
    ! [X,P] : 
      ( ( agent(X)
        & proposition(P) )
     => ( knows(X,P)
       => believes(X,P) ) ) ).

fof(agent_belief,axiom,
    ! [X,P] : 
      ( ( agent(X)
        & proposition(P) )
     => ( believes(X,P)
       => ~ believes(X,not(P)) ) ) ).

fof(agent_cooperation,axiom,
    ! [X,Y,G] : 
      ( ( agent(X)
        & agent(Y)
        & goal(G) )
     => ( ( wants(X,G)
          & wants(Y,G) )
       => cooperates(X,Y,G) ) ) ).

%------------------------------------------------------------------------------
