%------------------------------------------------------------------------------
% File     : SET001+0 : TPTP v9.0.0. Released v2.2.0.
% Domain   : Set Theory
% Axioms   : Set theory axioms based on Naive set theory
% Version  : [Pas99] axioms.
% English  :

% Refs     : [Pas99] <PERSON>re (1999), Email to G. Sutcliffe
% Source   : [Pas99]
% Names    :

% Status   : Satisfiable
% Syntax   : Number of formulae    :   12 (   3 unt;   0 def)
%            Number of atoms       :   30 (   3 equ)
%            Maximal formula atoms :    3 (   2 avg)
%            Number of connectives :   18 (   0   ~;   0   |;   7   &)
%                                         (  11 <=>;   0  =>;   0  <=;   0 <~>)
%            Maximal formula depth :    7 (   5 avg)
%            Maximal term depth    :    3 (   1 avg)
%            Number of predicates  :    4 (   3 usr;   0 prp; 1-2 aty)
%            Number of functors    :    4 (   4 usr;   0 con; 2-2 aty)
%            Number of variables   :   28 (  28   !;   0   ?)
% SPC      : FOF_SAT_RFO_SEQ

% Comments :
%------------------------------------------------------------------------------
%----Axioms for set theory
fof(subset_defn,axiom,
    ! [X,Y] : 
      ( subset(X,Y)
    <=> ! [Z] : 
          ( member(Z,X)
         => member(Z,Y) ) ) ).

fof(equality_defn,axiom,
    ! [X,Y] : 
      ( X = Y
    <=> ( subset(X,Y)
        & subset(Y,X) ) ) ).

fof(union_defn,axiom,
    ! [X,Y,Z] : 
      ( member(Z,union(X,Y))
    <=> ( member(Z,X)
        | member(Z,Y) ) ) ).

fof(intersection_defn,axiom,
    ! [X,Y,Z] : 
      ( member(Z,intersection(X,Y))
    <=> ( member(Z,X)
        & member(Z,Y) ) ) ).

fof(difference_defn,axiom,
    ! [X,Y,Z] : 
      ( member(Z,difference(X,Y))
    <=> ( member(Z,X)
        & ~ member(Z,Y) ) ) ).

fof(empty_set_defn,axiom,
    ! [X] : ~ member(X,empty_set) ).

fof(universal_set_defn,axiom,
    ! [X] : member(X,universal_set) ).

fof(complement_defn,axiom,
    ! [X,Y] : 
      ( member(Y,complement(X))
    <=> ~ member(Y,X) ) ).

%------------------------------------------------------------------------------
