%------------------------------------------------------------------------------
% File     : AGENT_PROBLEM+1 : Test problem with multiple includes
% Domain   : Agents and Set Theory
% Problem  : Test problem using both agent and set theory axioms
% Version  : Test version
% English  : Prove properties about agents and sets

% Status   : Theorem
% Syntax   : Mixed domain problem
% SPC      : FOF_THM_RFO_NEQ

% Comments : This problem includes axioms from multiple external files
%------------------------------------------------------------------------------
%----Include set theory axioms
include(Axioms/SET001+0.ax).

%----Include agent theory axioms  
include(Axioms/AGT001+1.ax).

%----Problem specific definitions
fof(agent_set_relation,axiom,
    ! [X,S] : 
      ( ( agent(X)
        & set_of_agents(S) )
     => member(X,S) ) ).

%----Problem specific facts
fof(alice_is_agent,hypothesis,
    agent(alice) ).

fof(bob_is_agent,hypothesis,
    agent(bob) ).

fof(agents_set_exists,hypothesis,
    set_of_agents(all_agents) ).

%----Goal to prove
fof(goal,conjecture,
    ( member(alice,all_agents)
    & member(bob,all_agents) ) ).

%------------------------------------------------------------------------------
