"""
自然语言到TPTP转换处理器
负责将自然语言描述转换为TPTP格式，并使用eprover进行验证和修复
"""

import os
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from ai_client import AIClient
# 保持向后兼容
ChatGPTClient = AIClient
from file_processor import FileProcessor
from eprover_validator import EProverValidator
from config import PromptTemplates, SystemMessages


class NLToTPTPProcessor:
    """自然语言到TPTP转换处理器"""
    
    def __init__(self, nl_dir: str = "./nl", transform_tptp_dir: str = "./transform_tptp",
                 result_dir: str = "./result", chatgpt_client: Optional[ChatGPTClient] = None,
                 eprover_validator: Optional[EProverValidator] = None,
                 max_fix_attempts: int = 5, enable_nl_preprocessing: bool = False,
                 enable_validation_classification: bool = True,
                 preprocessed_nl_dir: str = None, fix_steps_dir: str = None):
        """
        初始化处理器
        
        Args:
            nl_dir (str): 自然语言文件目录
            transform_tptp_dir (str): 转换后TPTP文件输出目录
            result_dir (str): 一致性判定结果文件目录
            chatgpt_client (ChatGPTClient, optional): ChatGPT客户端
            eprover_validator (EProverValidator, optional): EProver验证器
            max_fix_attempts (int): 最大修复尝试次数
            enable_nl_preprocessing (bool): 是否启用自然语言预处理
            enable_validation_classification (bool): 是否启用验证结果分类
        """
        self.nl_dir = Path(nl_dir)
        self.transform_tptp_dir = Path(transform_tptp_dir)
        self.result_dir = Path(result_dir)

        # 新增目录配置
        self.preprocessed_nl_dir = Path(preprocessed_nl_dir) if preprocessed_nl_dir else None
        self.fix_steps_dir = Path(fix_steps_dir) if fix_steps_dir else None
        self.chatgpt_client = chatgpt_client
        # 如果没有提供eprover_validator，使用配置中的路径创建
        if eprover_validator is None:
            from config import Config
            nl_config = Config.get_nl_to_tptp_config()
            self.eprover_validator = EProverValidator(eprover_path=nl_config["eprover_path"])
        else:
            self.eprover_validator = eprover_validator
        self.max_fix_attempts = max_fix_attempts
        self.enable_nl_preprocessing = enable_nl_preprocessing
        self.enable_validation_classification = enable_validation_classification

        # 创建输出目录
        self.transform_tptp_dir.mkdir(parents=True, exist_ok=True)
        self.result_dir.mkdir(parents=True, exist_ok=True)

        # 创建新增目录
        if self.preprocessed_nl_dir:
            self.preprocessed_nl_dir.mkdir(parents=True, exist_ok=True)
        if self.fix_steps_dir:
            self.fix_steps_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化文件处理器
        self.file_processor = FileProcessor(str(self.nl_dir), str(self.transform_tptp_dir))
    
    def process_all_nl_files(self) -> Dict[str, bool]:
        """
        处理所有自然语言文件，转换为TPTP格式
        
        Returns:
            Dict[str, bool]: 文件名到处理结果的映射
        """
        results = {}
        
        if not self.nl_dir.exists():
            self.logger.error(f"自然语言目录不存在: {self.nl_dir}")
            return results
        
        # 查找所有.nl文件
        nl_files = list(self.nl_dir.glob("*.nl"))
        
        if not nl_files:
            self.logger.warning(f"在目录 {self.nl_dir} 中未找到.nl文件")
            return results
        
        self.logger.info(f"找到 {len(nl_files)} 个自然语言文件")
        
        for nl_file in nl_files:
            try:
                self.logger.info(f"处理文件: {nl_file.name}")
                success = self.process_single_nl_file(nl_file)
                results[nl_file.name] = success
                
                if success:
                    self.logger.info(f"✓ 成功处理: {nl_file.name}")
                else:
                    self.logger.error(f"✗ 处理失败: {nl_file.name}")
                    
            except Exception as e:
                self.logger.error(f"处理文件 {nl_file.name} 时出错: {e}")
                results[nl_file.name] = False
        
        return results
    
    def process_single_nl_file(self, nl_file: Path) -> bool:
        """
        处理单个自然语言文件
        
        Args:
            nl_file (Path): 自然语言文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 读取自然语言文件内容
            content = self.file_processor.get_file_content(str(nl_file))
            if content is None:
                return False
            
            # 预处理自然语言（可选）
            processed_content = content
            if self.enable_nl_preprocessing and self.chatgpt_client:
                self.logger.info(f"启用自然语言预处理: {nl_file.name}")
                processed_content = self.preprocess_natural_language(content, nl_file.name)
                if processed_content is None:
                    self.logger.warning("自然语言预处理失败，使用原始内容")
                    processed_content = content

                    # 即使预处理失败，也保存原始内容供参考
                    self._save_preprocessed_nl(nl_file.name, content, is_failed=True)
                else:
                    self.logger.info("✓ 自然语言预处理完成")
            elif self.enable_nl_preprocessing:
                # 如果启用了预处理但没有AI客户端，保存原始内容
                self.logger.info(f"预处理已启用但无AI客户端，保存原始内容: {nl_file.name}")
                self._save_preprocessed_nl(nl_file.name, content, is_failed=True)

            # 转换为TPTP格式
            if self.chatgpt_client:
                tptp_content = self.convert_to_tptp(processed_content, nl_file.name)
                if tptp_content is None:
                    return False
            else:
                self.logger.warning("未提供ChatGPT客户端，跳过TPTP转换")
                return False

            # 生成输出文件名（.nl -> .p）
            output_filename = nl_file.stem + ".p"

            # 先保存转换后的TPTP文件到transform_tptp目录
            success = self.file_processor.write_output(
                content=tptp_content,
                filename=output_filename
            )

            if not success:
                self.logger.error(f"保存TPTP文件失败: {output_filename}")
                return False

            self.logger.info(f"✓ TPTP文件已保存: {output_filename}")

            # 然后读取保存的文件进行验证和比对
            saved_file_path = self.transform_tptp_dir / output_filename
            self._validate_and_compare_saved_file(saved_file_path, nl_file.stem)

            # 进行验证结果分类（如果启用）
            if self.enable_validation_classification:
                self._classify_validation_result(saved_file_path, nl_file.stem)

            return success
            
        except Exception as e:
            self.logger.error(f"处理文件 {nl_file} 时出错: {e}")
            return False
    
    def convert_to_tptp(self, nl_content: str, filename: str) -> Optional[str]:
        """
        使用ChatGPT将自然语言转换为TPTP格式
        
        Args:
            nl_content (str): 自然语言内容
            filename (str): 文件名
            
        Returns:
            Optional[str]: 转换后的TPTP内容，失败时返回None
        """
        if not self.chatgpt_client:
            self.logger.error("ChatGPT客户端未初始化")
            return None
        
        try:
            # 构建提示词
            prompt = self.build_nl_to_tptp_prompt(nl_content, filename)
            
            # 调用ChatGPT
            response = self.chatgpt_client.get_response(
                prompt=prompt,
                system_message=self.get_nl_to_tptp_system_message(),
                temperature=0.1,  # 使用很低的温度以获得更一致的结果
                max_tokens=4000
            )
            
            # 提取TPTP内容
            tptp_content = self.extract_tptp_from_response(response)
            
            return tptp_content
            
        except Exception as e:
            self.logger.error(f"转换文件 {filename} 时出错: {e}")
            return None
    
    def validate_and_fix_tptp_old(self, tptp_content: str, filename: str) -> Tuple[Optional[str], str]:
        """
        使用eprover验证TPTP内容，如果有错误则修复
        
        Args:
            tptp_content (str): TPTP内容
            filename (str): 文件名
            
        Returns:
            Tuple[Optional[str], str]: (修复后的TPTP内容，最终的eprover输出)，失败时返回(None, "")
        """
        current_content = tptp_content
        
        for attempt in range(self.max_fix_attempts):
            self.logger.info(f"验证尝试 {attempt + 1}/{self.max_fix_attempts}: {filename}")
            
            # 使用eprover验证
            is_valid, stdout, stderr = self.eprover_validator.validate_tptp_content(
                current_content, filename
            )
            
            if is_valid:
                self.logger.info(f"✓ TPTP语法验证通过: {filename}")
                return current_content, stdout
            
            # 如果验证失败，尝试修复
            self.logger.warning(f"✗ TPTP语法验证失败: {filename}")
            
            if attempt < self.max_fix_attempts - 1:  # 不是最后一次尝试
                # 提取错误信息
                error_message = self.eprover_validator.extract_error_message(stdout, stderr)
                
                # 使用ChatGPT修复
                fixed_content = self.fix_tptp_with_gpt(current_content, error_message, filename)
                
                if fixed_content:
                    current_content = fixed_content
                    self.logger.info(f"尝试修复TPTP内容: {filename}")
                else:
                    self.logger.error(f"GPT修复失败: {filename}")
                    break
            else:
                self.logger.error(f"达到最大修复尝试次数: {filename}")

        return None, ""  # 修复失败
    
    def fix_tptp_with_gpt(self, tptp_content: str, error_message: str, filename: str,
                         eprover_stdout: str = "", eprover_stderr: str = "") -> Optional[str]:
        """
        使用ChatGPT修复TPTP语法错误
        
        Args:
            tptp_content (str): 有错误的TPTP内容
            error_message (str): 错误信息
            filename (str): 文件名
            eprover_stdout (str): EProver标准输出
            eprover_stderr (str): EProver错误输出

        Returns:
            Optional[str]: 修复后的TPTP内容
        """
        if not self.chatgpt_client:
            return None
        
        try:
            # 构建修复提示词
            prompt = self.build_fix_tptp_prompt(tptp_content, error_message, filename, eprover_stdout, eprover_stderr)
            
            # 调用ChatGPT修复
            response = self.chatgpt_client.get_response(
                prompt=prompt,
                system_message=self.get_tptp_fix_system_message(),
                temperature=0.1,
                max_tokens=4000
            )
            
            # 提取修复后的TPTP内容
            fixed_content = self.extract_tptp_from_response(response)

            # 保存修复步骤信息
            if fixed_content:
                self._save_fix_step(filename, tptp_content, error_message, fixed_content,
                                  eprover_stdout, eprover_stderr, response)

            return fixed_content

        except Exception as e:
            self.logger.error(f"使用GPT修复TPTP时出错: {e}")
            return None
    
    def build_nl_to_tptp_prompt(self, nl_content: str, filename: str) -> str:
        """构建自然语言到TPTP转换的提示词"""
        prompt = f"""
请将以下自然语言描述转换为标准的TPTP（Thousands of Problems for Theorem Provers）格式。

文件名: {filename}

自然语言内容:
{nl_content}

转换要求：
1. **严格遵循TPTP语法**: 使用正确的fof()和cnf()语法
2. **保持逻辑完整性**: 确保所有逻辑关系都正确表达
3. **使用标准角色**: axiom, hypothesis, definition, conjecture等
4. **正确的量词**: !{{[X]}} (全称量词), ?{{[X]}} (存在量词)
5. **正确的连接词**: & (与), | (或), => (蕴含), <=> (等价), ~ (非)
6. **括号匹配**: 确保所有括号正确匹配
7. **语句结束**: 每个fof语句以点号(.)结束

输出格式：
- 只输出TPTP格式的内容
- 包含必要的注释（以%开头）
- 确保语法完全正确

请开始转换：
"""
        return prompt
    
    def build_fix_tptp_prompt(self, tptp_content: str, error_message: str, filename: str,
                             eprover_stdout: str = "", eprover_stderr: str = "") -> str:
        """构建TPTP修复的提示词"""
        return PromptTemplates.TPTP_FIX.format(
            tptp_content=tptp_content,
            error_message=error_message,
            eprover_stdout=eprover_stdout,
            eprover_stderr=eprover_stderr
        )
    
    def get_nl_to_tptp_system_message(self) -> str:
        """获取自然语言到TPTP转换的系统消息"""
        return """你是一个专业的形式化逻辑专家，精通TPTP格式和一阶逻辑。你的任务是将自然语言描述准确地转换为符合TPTP标准的形式化逻辑表达式。

你必须：
1. 严格遵循TPTP语法规范
2. 确保生成的代码能通过eprover验证
3. 保持逻辑的准确性和完整性
4. 使用正确的量词、连接词和语法结构
5. 生成的代码必须语法正确，无任何错误"""
    
    def get_tptp_fix_system_message(self) -> str:
        """获取TPTP修复的系统消息"""
        return """你是一个TPTP语法专家，专门负责修复TPTP代码中的语法错误。你必须：

1. 仔细分析eprover提供的错误信息
2. 准确定位语法错误的位置
3. 进行最小化的修复，不改变原有逻辑
4. 确保修复后的代码完全符合TPTP语法规范
5. 生成能通过eprover验证的正确代码

你对TPTP语法有深入的理解，能够快速识别和修复各种语法错误。"""
    
    def extract_tptp_from_response(self, response: str) -> str:
        """从GPT响应中提取TPTP内容"""
        # 移除可能的markdown代码块标记
        lines = response.split('\n')
        tptp_lines = []
        in_code_block = False
        
        for line in lines:
            # 检查是否是代码块开始/结束
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue
            
            # 如果在代码块中，或者行看起来像TPTP内容，则包含
            if in_code_block or self._looks_like_tptp_line(line):
                tptp_lines.append(line)
        
        return '\n'.join(tptp_lines).strip()
    
    def _looks_like_tptp_line(self, line: str) -> bool:
        """判断一行是否看起来像TPTP内容"""
        line = line.strip()
        if not line:
            return True  # 空行
        
        # TPTP注释
        if line.startswith('%'):
            return True
        
        # TPTP语句
        tptp_keywords = ['fof(', 'cnf(', 'include(', 'thf(', 'tff(']
        if any(line.startswith(keyword) for keyword in tptp_keywords):
            return True
        
        # 可能是语句的继续行
        if any(char in line for char in ['(', ')', '[', ']', '&', '|', '=>', '<=>', '~', '!']):
            return True
        
        return False
    
    def get_processing_stats(self) -> Dict[str, int]:
        """获取处理统计信息"""
        stats = {
            'nl_files': 0,
            'transform_tptp_files': 0,
            'result_files': 0,
            'valid_problems': 0,
            'invalid_problems': 0,
            'error_problems': 0
        }

        if self.nl_dir.exists():
            stats['nl_files'] = len(list(self.nl_dir.glob("*.nl")))

        if self.transform_tptp_dir.exists():
            stats['transform_tptp_files'] = len(list(self.transform_tptp_dir.glob("*.p")))

        if self.result_dir.exists():
            stats['result_files'] = len(list(self.result_dir.glob("*.txt")))

            # 统计分类文件
            valid_file = self.result_dir / "valid_problems.txt"
            invalid_file = self.result_dir / "invalid_problems.txt"
            error_file = self.result_dir / "error_problems.txt"

            if valid_file.exists():
                with open(valid_file, 'r', encoding='utf-8') as f:
                    stats['valid_problems'] = len(f.readlines())

            if invalid_file.exists():
                with open(invalid_file, 'r', encoding='utf-8') as f:
                    stats['invalid_problems'] = len(f.readlines())

            if error_file.exists():
                with open(error_file, 'r', encoding='utf-8') as f:
                    stats['error_problems'] = len(f.readlines())

        return stats

    def preprocess_natural_language(self, content: str, filename: str) -> Optional[str]:
        """
        预处理自然语言，简化表达并确保无歧义

        Args:
            content (str): 原始自然语言内容
            filename (str): 文件名

        Returns:
            Optional[str]: 预处理后的自然语言内容，失败时返回None
        """
        try:
            self.logger.info(f"开始预处理自然语言: {filename}")

            # 构建预处理提示词
            prompt = PromptTemplates.NL_PREPROCESSING.format(content=content)

            # 调用ChatGPT进行预处理
            response = self.chatgpt_client.get_response_with_context(
                messages=[
                    {"role": "system", "content": SystemMessages.NL_PREPROCESSING_EXPERT},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3  # 使用较低的温度确保一致性
            )

            if response:
                processed_content = response.strip()

                # 验证预处理结果（放宽验证条件）
                is_valid = True
                validation_warnings = []

                if len(processed_content) < 20:
                    validation_warnings.append("预处理结果过短")
                    is_valid = False

                # 检查是否包含明显的指令文本（更精确的检查）
                instruction_keywords = ["请对以下", "请输出", "处理要求", "预处理后的描述"]
                if any(keyword in processed_content for keyword in instruction_keywords):
                    validation_warnings.append("预处理结果包含指令文本")
                    is_valid = False

                if validation_warnings:
                    self.logger.warning(f"预处理验证警告 {filename}: {'; '.join(validation_warnings)}")

                if is_valid:
                    self.logger.info(f"✓ 自然语言预处理成功: {filename}")
                    self.logger.debug(f"预处理前长度: {len(content)}, 预处理后长度: {len(processed_content)}")

                    # 保存预处理后的自然语言文件
                    self._save_preprocessed_nl(filename, processed_content)
                    return processed_content
                else:
                    self.logger.warning(f"预处理结果验证失败，但仍保存结果: {filename}")

                    # 即使验证失败，也保存结果供用户检查
                    self._save_preprocessed_nl(filename, processed_content, is_failed=True)

                    # 返回None表示预处理失败，使用原始内容
                    return None
            else:
                self.logger.error(f"预处理API调用失败: {filename}")

                # 保存原始内容到预处理目录，标记为失败
                self._save_preprocessed_nl(filename, content, is_failed=True)
                return None

        except Exception as e:
            self.logger.error(f"预处理自然语言时出错 {filename}: {e}")
            return None

    def _save_preprocessed_nl(self, filename: str, preprocessed_content: str, is_failed: bool = False):
        """保存预处理后的自然语言文件"""
        if not self.preprocessed_nl_dir:
            return

        try:
            # 生成预处理文件名（保持与原始问题名称对应）
            # 从filename中提取问题名称（去掉.nl扩展名）
            problem_name = filename.replace('.nl', '') if filename.endswith('.nl') else filename

            # 如果预处理失败，在文件名中标记
            if is_failed:
                preprocessed_file = self.preprocessed_nl_dir / f"{problem_name}_failed.nl"
            else:
                preprocessed_file = self.preprocessed_nl_dir / f"{problem_name}.nl"

            # 添加文件头信息
            file_content = f"""% 预处理文件: {filename}
% 预处理状态: {'失败' if is_failed else '成功'}
% 生成时间: {self._get_current_timestamp()}

{preprocessed_content}
"""

            with open(preprocessed_file, 'w', encoding='utf-8') as f:
                f.write(file_content)

            status = "失败结果" if is_failed else "成功结果"
            self.logger.info(f"预处理{status}已保存: {preprocessed_file}")

        except Exception as e:
            self.logger.error(f"保存预处理文件失败: {e}")

    def _save_fix_step(self, filename: str, original_content: str, error_message: str,
                      fixed_content: str, eprover_stdout: str, eprover_stderr: str,
                      gpt_response: str):
        """保存TPTP修复步骤信息"""
        if not self.fix_steps_dir:
            return

        try:
            # 生成修复步骤文件名
            # 从filename中提取问题名称（去掉扩展名）
            problem_name = filename.replace('.p', '').replace('.nl', '') if '.' in filename else filename

            # 获取当前修复步骤号（通过检查已存在的文件数量）
            step_number = self._get_next_step_number(problem_name)

            fix_step_file = self.fix_steps_dir / f"{problem_name}_{step_number}.txt"

            # 构建修复步骤信息
            step_info = f"""TPTP修复步骤 #{step_number}
问题文件: {filename}
修复时间: {self._get_current_timestamp()}

=== 原始TPTP内容 ===
{original_content}

=== 错误信息 ===
{error_message}

=== EProver标准输出 ===
{eprover_stdout}

=== EProver错误输出 ===
{eprover_stderr}

=== GPT修复响应 ===
{gpt_response}

=== 修复后TPTP内容 ===
{fixed_content}

=== 修复完成 ===
"""

            with open(fix_step_file, 'w', encoding='utf-8') as f:
                f.write(step_info)

            # 同时保存修复后的完整TPTP文件
            fixed_tptp_file = self.fix_steps_dir / f"{problem_name}_{step_number}.p"
            with open(fixed_tptp_file, 'w', encoding='utf-8') as f:
                f.write(fixed_content)

            self.logger.info(f"修复步骤已保存: {fix_step_file}")
            self.logger.info(f"修复后TPTP文件已保存: {fixed_tptp_file}")

        except Exception as e:
            self.logger.error(f"保存修复步骤失败: {e}")

    def _get_next_step_number(self, problem_name: str) -> int:
        """获取下一个修复步骤号"""
        if not self.fix_steps_dir.exists():
            return 1

        # 查找已存在的修复步骤文件
        existing_files = list(self.fix_steps_dir.glob(f"{problem_name}_*.txt"))

        if not existing_files:
            return 1

        # 提取步骤号并找到最大值
        step_numbers = []
        for file in existing_files:
            try:
                # 从文件名中提取步骤号
                filename = file.stem  # 去掉扩展名
                if '_' in filename:
                    step_part = filename.split('_')[-1]
                    if step_part.isdigit():
                        step_numbers.append(int(step_part))
            except:
                continue

        return max(step_numbers) + 1 if step_numbers else 1

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _extract_and_compare_results(self, problem_name: str, tptp_content: str, eprover_output: str):
        """
        提取判定结果并与原始问题比较

        Args:
            problem_name (str): 问题名称（不含扩展名）
            tptp_content (str): 转换后的TPTP内容
            eprover_output (str): eprover输出
        """
        try:
            # 提取转换后问题的判定结果
            converted_result = self._extract_szs_status(eprover_output)

            # 获取原始问题的判定结果
            original_result = self._get_original_problem_result(problem_name)

            # 保存比较结果
            self._save_comparison_result(problem_name, converted_result, original_result)

            self.logger.info(f"判定结果比较 - {problem_name}: 转换后={converted_result}, 原始={original_result}")

        except Exception as e:
            self.logger.error(f"提取和比较结果时出错 {problem_name}: {e}")

    def _extract_szs_status(self, eprover_output: str) -> str:
        """
        从eprover输出中提取SZS状态

        Args:
            eprover_output (str): eprover输出

        Returns:
            str: 提取的状态，如"Theorem", "Unsatisfiable"等
        """
        import re

        # 查找 "# SZS status XXX" 模式
        szs_pattern = r'#\s*SZS\s+status\s+(\w+)'
        match = re.search(szs_pattern, eprover_output, re.IGNORECASE)

        if match:
            return match.group(1)
        else:
            # 如果没有找到SZS状态，返回"Unknown"
            return "Unknown"

    def _get_original_problem_result(self, problem_name: str) -> str:
        """
        获取原始问题的判定结果

        Args:
            problem_name (str): 问题名称

        Returns:
            str: 原始问题的判定结果
        """
        try:
            # 构建原始问题文件路径
            original_file = Path(self.nl_dir).parent / "tptp" / f"{problem_name}.p"

            if not original_file.exists():
                self.logger.warning(f"原始问题文件不存在: {original_file}")
                return "FileNotFound"

            # 使用eprover验证原始问题
            is_valid, stdout, stderr = self.eprover_validator.validate_tptp_file(str(original_file))

            if is_valid:
                return self._extract_szs_status(stdout)
            else:
                self.logger.warning(f"原始问题验证失败: {problem_name}")
                return "ValidationFailed"

        except Exception as e:
            self.logger.error(f"获取原始问题结果时出错 {problem_name}: {e}")
            return "Error"

    def _save_comparison_result(self, problem_name: str, converted_result: str, original_result: str):
        """
        保存比较结果到文件

        Args:
            problem_name (str): 问题名称
            converted_result (str): 转换后的结果
            original_result (str): 原始问题的结果
        """
        try:
            # 结果文件路径（保存到result目录）
            result_file = self.result_dir / "comparison_results.txt"

            # 判断结果是否一致
            is_consistent = converted_result == original_result
            consistency = "一致" if is_consistent else "不一致"

            # 格式：问题名称,转换后结果,原始结果,一致性;
            result_line = f"{problem_name},{converted_result},{original_result},{consistency};\n"

            # 追加写入文件
            with open(result_file, 'a', encoding='utf-8') as f:
                f.write(result_line)

            self.logger.info(f"比较结果已保存: {problem_name} - {consistency}")

        except Exception as e:
            self.logger.error(f"保存比较结果时出错 {problem_name}: {e}")

    def _classify_validation_result(self, tptp_file_path: Path, problem_name: str):
        """
        对验证结果进行分类，保存到result文件夹

        Args:
            tptp_file_path (Path): TPTP文件路径
            problem_name (str): 问题名称
        """
        try:
            self.logger.info(f"开始验证结果分类: {problem_name}")

            # 读取TPTP文件内容
            with open(tptp_file_path, 'r', encoding='utf-8') as f:
                tptp_content = f.read()

            # 使用eprover验证
            is_valid, stdout, stderr = self.eprover_validator.validate_tptp_content(tptp_content, problem_name)

            # 确定分类
            if is_valid:
                # 格式无误
                classification = "VALID"
                status_detail = "语法正确"

                # 尝试提取SZS状态
                szs_status = self._extract_szs_status(stdout)
                if szs_status:
                    status_detail = f"语法正确, SZS状态: {szs_status}"

                self.logger.info(f"✓ 验证通过: {problem_name} - {status_detail}")

                # 保存到valid列表
                self._save_to_classification_file("valid_problems.txt", problem_name, status_detail, tptp_file_path)

            else:
                # 有错误
                classification = "INVALID"

                # 提取错误信息
                error_message = self.eprover_validator.extract_error_message(stdout, stderr)
                error_summary = self._extract_error_summary(error_message)

                self.logger.info(f"✗ 验证失败: {problem_name} - {error_summary}")

                # 保存到invalid列表
                self._save_to_classification_file("invalid_problems.txt", problem_name, error_summary, tptp_file_path)

            # 保存到总体分类文件
            self._save_to_classification_summary(problem_name, classification, status_detail if is_valid else error_summary)

        except Exception as e:
            self.logger.error(f"验证结果分类时出错 {problem_name}: {e}")
            # 保存错误记录
            self._save_to_classification_file("error_problems.txt", problem_name, f"分类错误: {str(e)}", tptp_file_path)

    def _extract_error_summary(self, error_message: str) -> str:
        """
        提取错误信息摘要

        Args:
            error_message (str): 完整错误信息

        Returns:
            str: 错误摘要
        """
        # 常见错误类型映射
        error_patterns = {
            "used with arity": "参数数量不匹配",
            "registered with arity": "函数参数错误",
            "formula has free variables": "自由变量错误",
            "syntax error": "语法错误",
            "parse error": "解析错误",
            "unexpected token": "意外标记",
            "cannot parse": "无法解析",
            "undeclared function": "未声明函数",
            "undeclared predicate": "未声明谓词"
        }

        error_lower = error_message.lower()

        for pattern, summary in error_patterns.items():
            if pattern in error_lower:
                return summary

        # 如果没有匹配到已知模式，返回简化的错误信息
        lines = error_message.split('\n')
        for line in lines:
            if 'error' in line.lower() or 'column' in line.lower():
                return line.strip()[:100]  # 限制长度

        return "未知错误"

    def _save_to_classification_file(self, filename: str, problem_name: str, status: str, tptp_file_path: Path):
        """
        保存到分类文件

        Args:
            filename (str): 文件名
            problem_name (str): 问题名称
            status (str): 状态描述
            tptp_file_path (Path): TPTP文件路径
        """
        try:
            classification_file = self.result_dir / filename

            # 获取文件大小
            file_size = tptp_file_path.stat().st_size if tptp_file_path.exists() else 0

            # 获取当前时间
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 格式：时间戳,问题名称,状态,文件大小,文件路径
            record = f"{timestamp},{problem_name},{status},{file_size},{tptp_file_path.name}\n"

            # 追加写入
            with open(classification_file, 'a', encoding='utf-8') as f:
                f.write(record)

            self.logger.debug(f"分类记录已保存到: {filename}")

        except Exception as e:
            self.logger.error(f"保存分类文件时出错 {filename}: {e}")

    def _save_to_classification_summary(self, problem_name: str, classification: str, detail: str):
        """
        保存到分类汇总文件

        Args:
            problem_name (str): 问题名称
            classification (str): 分类（VALID/INVALID）
            detail (str): 详细信息
        """
        try:
            summary_file = self.result_dir / "validation_summary.txt"

            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 格式：时间戳,问题名称,分类,详细信息
            record = f"{timestamp},{problem_name},{classification},{detail}\n"

            # 追加写入
            with open(summary_file, 'a', encoding='utf-8') as f:
                f.write(record)

        except Exception as e:
            self.logger.error(f"保存分类汇总时出错: {e}")

    def _validate_and_compare_saved_file(self, saved_file_path: Path, problem_name: str):
        """
        验证保存的TPTP文件并进行比对

        Args:
            saved_file_path (Path): 保存的TPTP文件路径
            problem_name (str): 问题名称
        """
        try:
            self.logger.info(f"开始验证保存的TPTP文件: {saved_file_path}")

            # 读取保存的文件内容
            with open(saved_file_path, 'r', encoding='utf-8') as f:
                tptp_content = f.read()

            # 使用新的验证逻辑
            can_determine, result_or_error = self.eprover_validator.can_determine_problem(tptp_content, problem_name)

            if can_determine:
                self.logger.info(f"✓ EProver能够处理此问题: {problem_name}, 结果: {result_or_error}")

                # 获取原始问题的判定结果
                original_result = self._get_original_problem_result(problem_name)

                # 保存比较结果
                self._save_comparison_result(problem_name, result_or_error, original_result)

                self.logger.info(f"判定结果比较 - {problem_name}: 转换后={result_or_error}, 原始={original_result}")

            else:
                # 语法错误，需要修复
                self.logger.warning(f"✗ TPTP文件有语法错误: {problem_name}, 错误类型: {result_or_error}")

                # 尝试修复
                self._attempt_iterative_fix(saved_file_path, problem_name, tptp_content)

        except Exception as e:
            self.logger.error(f"验证保存的文件时出错 {problem_name}: {e}")
            # 保存错误记录
            self._save_comparison_result(problem_name, "ProcessError", "Unknown")



    def _attempt_iterative_fix(self, saved_file_path: Path, problem_name: str, initial_content: str):
        """
        迭代修复TPTP文件直到eprover能够正常判定

        Args:
            saved_file_path (Path): 保存的TPTP文件路径
            problem_name (str): 问题名称
            initial_content (str): 初始TPTP内容
        """
        current_content = initial_content

        for attempt in range(self.max_fix_attempts):
            self.logger.info(f"修复尝试 {attempt + 1}/{self.max_fix_attempts}: {problem_name}")

            # 检查当前内容是否能被eprover正常处理
            can_determine, result_or_error = self.eprover_validator.can_determine_problem(current_content, problem_name)

            if can_determine:
                # eprover能够正常判定，停止修复
                self.logger.info(f"✓ 修复成功，EProver能够判定: {problem_name}, 结果: {result_or_error}")

                # 保存修复后的文件
                with open(saved_file_path, 'w', encoding='utf-8') as f:
                    f.write(current_content)

                # 获取原始问题的判定结果并比较
                original_result = self._get_original_problem_result(problem_name)
                self._save_comparison_result(problem_name, result_or_error, original_result)

                self.logger.info(f"判定结果比较 - {problem_name}: 转换后={result_or_error}, 原始={original_result}")
                return

            # 还有语法错误，继续修复
            if attempt < self.max_fix_attempts - 1:  # 不是最后一次尝试
                # 获取详细错误信息
                _, stdout, stderr = self.eprover_validator.validate_tptp_content(current_content, problem_name)
                error_message = self.eprover_validator.extract_error_message(stdout, stderr)

                self.logger.info(f"继续修复，完整错误信息:")
                self.logger.info(f"{error_message}")

                # 使用GPT修复，传递完整的eprover输出
                fixed_content = self.fix_tptp_with_gpt(current_content, error_message, problem_name, stdout, stderr)

                if fixed_content and fixed_content != current_content:
                    current_content = fixed_content
                    self.logger.info(f"GPT修复完成，尝试下一轮验证")
                else:
                    self.logger.warning(f"GPT修复失败或无变化，停止修复")
                    break
            else:
                self.logger.error(f"达到最大修复尝试次数，修复失败: {problem_name}")

        # 修复失败，保存失败记录
        self.logger.error(f"✗ 修复失败: {problem_name}")
        self._save_comparison_result(problem_name, "FixFailed", "Unknown")
