"""
文件处理类
负责读取指定文件目录中的文件，遍历解析文件获取单个文件作为待输入字符串
同时提供将输出写入到指定文件夹的指定文件的功能
"""

import os
import json
import logging
from typing import List, Dict, Optional, Generator
from pathlib import Path
import chardet


class FileProcessor:
    """文件处理类"""
    
    def __init__(self, input_dir: str, output_dir: str):
        """
        初始化文件处理器
        
        Args:
            input_dir (str): 输入文件目录路径
            output_dir (str): 输出文件目录路径
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        
        # 创建输出目录（如果不存在）
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 支持的文件扩展名
        self.supported_extensions = {
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json', 
            '.xml', '.csv', '.log', '.yaml', '.yml', '.ini', '.cfg', '.p' ,'.ax'
        }
    
    def read_files(self, file_pattern: str = "*", recursive: bool = True) -> Generator[Dict[str, str], None, None]:
        """
        遍历读取目录中的文件
        
        Args:
            file_pattern (str): 文件匹配模式，默认为所有文件
            recursive (bool): 是否递归遍历子目录
            
        Yields:
            Dict[str, str]: 包含文件信息的字典，包含 'path', 'name', 'content' 键
        """
        if not self.input_dir.exists():
            self.logger.error(f"输入目录不存在: {self.input_dir}")
            return
        
        # 选择遍历方式
        if recursive:
            files = self.input_dir.rglob(file_pattern)
        else:
            files = self.input_dir.glob(file_pattern)
        
        for file_path in files:
            if file_path.is_file() and self._is_supported_file(file_path):
                try:
                    content = self.get_file_content(str(file_path))
                    if content is not None:
                        yield {
                            'path': str(file_path),
                            'name': file_path.name,
                            'relative_path': str(file_path.relative_to(self.input_dir)),
                            'content': content
                        }
                except Exception as e:
                    self.logger.error(f"读取文件失败 {file_path}: {e}")
    
    def get_file_content(self, file_path: str) -> Optional[str]:
        """
        读取单个文件内容作为字符串
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Optional[str]: 文件内容，如果读取失败返回 None
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None
            
            # 检测文件编码
            encoding = self._detect_encoding(file_path)
            
            # 读取文件内容
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                content = f.read()
            
            self.logger.info(f"成功读取文件: {file_path}")
            return content
            
        except Exception as e:
            self.logger.error(f"读取文件失败 {file_path}: {e}")
            return None
    
    def write_output(self, content: str, filename: str, subfolder: str = "") -> bool:
        """
        将输出写入到指定文件夹的指定文件
        
        Args:
            content (str): 要写入的内容
            filename (str): 文件名
            subfolder (str): 子文件夹名称（可选）
            
        Returns:
            bool: 写入是否成功
        """
        try:
            # 构建完整的输出路径
            if subfolder:
                output_path = self.output_dir / subfolder
                output_path.mkdir(parents=True, exist_ok=True)
            else:
                output_path = self.output_dir
            
            file_path = output_path / filename
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"成功写入文件: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"写入文件失败 {filename}: {e}")
            return False
    
    def write_json_output(self, data: Dict, filename: str, subfolder: str = "") -> bool:
        """
        将数据以JSON格式写入文件
        
        Args:
            data (Dict): 要写入的数据
            filename (str): 文件名
            subfolder (str): 子文件夹名称（可选）
            
        Returns:
            bool: 写入是否成功
        """
        try:
            json_content = json.dumps(data, ensure_ascii=False, indent=2)
            return self.write_output(json_content, filename, subfolder)
        except Exception as e:
            self.logger.error(f"写入JSON文件失败 {filename}: {e}")
            return False
    
    def get_file_list(self, file_pattern: str = "*", recursive: bool = True) -> List[str]:
        """
        获取目录中的文件列表
        
        Args:
            file_pattern (str): 文件匹配模式
            recursive (bool): 是否递归遍历
            
        Returns:
            List[str]: 文件路径列表
        """
        file_list = []
        
        if not self.input_dir.exists():
            self.logger.error(f"输入目录不存在: {self.input_dir}")
            return file_list
        
        if recursive:
            files = self.input_dir.rglob(file_pattern)
        else:
            files = self.input_dir.glob(file_pattern)
        
        for file_path in files:
            if file_path.is_file() and self._is_supported_file(file_path):
                file_list.append(str(file_path))
        
        return file_list
    
    def _is_supported_file(self, file_path: Path) -> bool:
        """
        检查文件是否为支持的格式
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            bool: 是否支持该文件格式
        """
        return file_path.suffix.lower() in self.supported_extensions
    
    def _detect_encoding(self, file_path: Path) -> str:
        """
        检测文件编码
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            str: 检测到的编码格式
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB用于检测编码
            
            result = chardet.detect(raw_data)
            encoding = result['encoding']
            
            # 如果检测失败，使用默认编码
            if not encoding:
                encoding = 'utf-8'
            
            return encoding
            
        except Exception:
            return 'utf-8'  # 默认使用UTF-8
    
    def add_supported_extension(self, extension: str):
        """
        添加支持的文件扩展名
        
        Args:
            extension (str): 文件扩展名（包含点号，如 '.txt'）
        """
        self.supported_extensions.add(extension.lower())
        self.logger.info(f"添加支持的文件扩展名: {extension}")
    
    def get_file_stats(self) -> Dict[str, int]:
        """
        获取输入目录的文件统计信息
        
        Returns:
            Dict[str, int]: 包含文件统计信息的字典
        """
        stats = {
            'total_files': 0,
            'supported_files': 0,
            'total_size': 0
        }
        
        if not self.input_dir.exists():
            return stats
        
        for file_path in self.input_dir.rglob('*'):
            if file_path.is_file():
                stats['total_files'] += 1
                stats['total_size'] += file_path.stat().st_size
                
                if self._is_supported_file(file_path):
                    stats['supported_files'] += 1
        
        return stats
