"""
TPTP到自然语言转换主程序
实现从TPTP子句到自然语言的转换工作流程
"""

import os
import sys
from pathlib import Path
from ai_client import AIClient
from tptp_processor import TPTPProcessor
from config import Config


def main(ai_provider_override=None):
    """主函数 - 执行TPTP到自然语言的转换"""

    print("=" * 60)
    print("TPTP到自然语言转换工具")
    print("=" * 60)

    # 获取配置
    ai_config = Config.get_ai_config()
    tptp_config = Config.get_tptp_config()

    # 如果有命令行参数覆盖，使用覆盖值
    if ai_provider_override is not None:
        ai_config["provider"] = ai_provider_override
        # 重新获取配置以应用新的提供商
        original_provider = os.environ.get("AI_PROVIDER", "openai")
        os.environ["AI_PROVIDER"] = ai_provider_override
        ai_config = Config.get_ai_config()
        # 恢复原始环境变量
        os.environ["AI_PROVIDER"] = original_provider

    # 验证配置（传递模型提供商覆盖参数）
    config_errors = Config.validate_config(ai_provider_override)
    if config_errors:
        print("配置验证失败:")
        for error in config_errors:
            print(f"  - {error}")
        print("\n请检查配置后重试。")
        return
    
    try:
        # 初始化AI客户端
        provider_name = ai_config["provider"].upper()
        print(f"初始化{provider_name}客户端...")
        ai_client = AIClient.create_from_config(ai_config)

        # 显示模型信息
        model_info = ai_client.get_model_info()
        print(f"✓ 使用模型: {model_info['provider'].upper()} - {model_info['model']}")
        
        # 初始化TPTP处理器
        print("初始化TPTP处理器...")
        tptp_processor = TPTPProcessor(
            tptp_dir=tptp_config["tptp_dir"],
            nl_dir=tptp_config["nl_dir"],
            chatgpt_client=ai_client
        )
        
        # 获取处理统计信息
        stats = tptp_processor.get_processing_stats()
        print(f"\n处理统计:")
        print(f"  - TPTP文件数量: {stats['tptp_files']}")
        print(f"  - 公理文件数量: {stats['axiom_files']}")
        print(f"  - 已有自然语言文件: {stats['nl_files']}")

        # 显示AI模型信息
        print(f"  - AI模型提供商: {model_info['provider'].upper()}")
        print(f"  - 使用模型: {model_info['model']}")
        
        if stats['tptp_files'] == 0:
            print(f"\n在目录 {tptp_config['tptp_dir']} 中未找到.p文件")
            print("请确保TPTP文件存在于正确的目录中。")
            return
        
        # 询问用户是否继续
        if stats['nl_files'] > 0:
            response = input(f"\n检测到 {stats['nl_files']} 个已存在的自然语言文件。是否继续处理？(y/n): ")
            if response.lower() != 'y':
                print("操作已取消。")
                return
        
        print(f"\n开始处理 {stats['tptp_files']} 个TPTP文件...")
        print("-" * 50)
        
        # 处理所有TPTP文件
        results = tptp_processor.process_all_tptp_files()
        
        # 统计结果
        successful = sum(1 for success in results.values() if success)
        failed = len(results) - successful
        
        print("\n" + "=" * 50)
        print("处理完成！")
        print(f"成功处理: {successful} 个文件")
        print(f"处理失败: {failed} 个文件")
        
        if failed > 0:
            print("\n失败的文件:")
            for filename, success in results.items():
                if not success:
                    print(f"  - {filename}")
        
        print(f"\n输出目录: {tptp_config['nl_dir']}")
        
        # 显示输出文件列表
        nl_files = list(Path(tptp_config['nl_dir']).glob("*.nl"))
        if nl_files:
            print(f"\n生成的自然语言文件:")
            for nl_file in sorted(nl_files):
                print(f"  - {nl_file.name}")
        
    except KeyboardInterrupt:
        print("\n\n操作被用户中断。")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()


def create_sample_tptp_files():
    """创建示例TPTP文件用于测试"""
    
    tptp_dir = Path("./tptp")
    axioms_dir = tptp_dir / "Axioms"
    
    # 创建目录
    tptp_dir.mkdir(exist_ok=True)
    axioms_dir.mkdir(exist_ok=True)
    
    # 创建示例公理文件
    axiom_content = """% Axioms for AGT001
% Basic agent theory axioms

fof(agent_exists, axiom, 
    ![X]: (agent(X) => exists(X))).

fof(action_requires_agent, axiom,
    ![X,A]: (performs(X,A) => agent(X))).

fof(goal_directed_action, axiom,
    ![X,A,G]: ((agent(X) & has_goal(X,G) & achieves(A,G)) => can_perform(X,A))).
"""
    
    axiom_file = axioms_dir / "AGT001+1.ax"
    with open(axiom_file, 'w', encoding='utf-8') as f:
        f.write(axiom_content)
    
    # 创建示例TPTP问题文件
    tptp_content = """% AGT001+1 : Agent Theory Problem
% This problem deals with basic agent reasoning

include(Axioms/AGT001+1.ax).

fof(agent_alice, axiom, agent(alice)).

fof(goal_alice, axiom, has_goal(alice, reach_destination)).

fof(action_move, axiom, achieves(move_forward, reach_destination)).

fof(prove_alice_can_move, conjecture,
    can_perform(alice, move_forward)).
"""
    
    tptp_file = tptp_dir / "AGT001+1.p"
    with open(tptp_file, 'w', encoding='utf-8') as f:
        f.write(tptp_content)
    
    print(f"创建示例文件:")
    print(f"  - {axiom_file}")
    print(f"  - {tptp_file}")


def setup_directories():
    """设置必要的目录结构"""
    
    directories = ["./tptp", "./tptp/Axioms", "./nl"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"确保目录存在: {directory}")


def show_help():
    """显示帮助信息"""
    
    help_text = """
TPTP到自然语言转换工具使用说明

用法:
    python tptp_to_nl.py [选项]

选项:
    --help, -h              显示此帮助信息
    --setup                 设置目录结构并创建示例文件
    --stats                 仅显示统计信息，不进行转换
    --model-provider NAME   设置AI模型提供商（openai/doubao/deepseek，默认openai）

目录结构:
    ./tptp/         TPTP问题文件目录（.p文件）
    ./tptp/Axioms/  公理文件目录（.ax文件）
    ./nl/           自然语言输出目录（.nl文件）

配置:
    请在运行前设置环境变量 OPENAI_API_KEY 或修改 config.py 中的 API 密钥

示例:
    # 设置目录并创建示例文件
    python tptp_to_nl.py --setup
    
    # 执行转换
    python tptp_to_nl.py

    # 使用不同AI模型
    python tptp_to_nl.py --model-provider openai
    python tptp_to_nl.py --model-provider doubao
    python tptp_to_nl.py --model-provider deepseek

    # 仅查看统计信息
    python tptp_to_nl.py --stats
"""
    print(help_text)


if __name__ == "__main__":
    # 检查命令行参数
    ai_provider = None

    i = 1
    while i < len(sys.argv):
        arg = sys.argv[i]

        if arg in ["--help", "-h"]:
            show_help()
            sys.exit(0)
        elif arg == "--setup":
            print("设置目录结构...")
            setup_directories()
            print("\n创建示例文件...")
            create_sample_tptp_files()
            print("\n设置完成！现在可以运行: python tptp_to_nl.py")
            sys.exit(0)
        elif arg == "--stats":
            tptp_config = Config.get_tptp_config()
            processor = TPTPProcessor(
                tptp_dir=tptp_config["tptp_dir"],
                nl_dir=tptp_config["nl_dir"]
            )
            stats = processor.get_processing_stats()
            print("处理统计:")
            print(f"  - TPTP文件数量: {stats['tptp_files']}")
            print(f"  - 公理文件数量: {stats['axiom_files']}")
            print(f"  - 已有自然语言文件: {stats['nl_files']}")
            sys.exit(0)
        elif arg == "--model-provider":
            if i + 1 < len(sys.argv):
                provider = sys.argv[i + 1].lower()
                if provider in ["openai", "doubao", "deepseek"]:
                    ai_provider = provider
                    print(f"✓ 设置AI模型提供商: {provider.upper()}")
                    i += 1  # 跳过下一个参数
                else:
                    print(f"错误：不支持的模型提供商 '{provider}'，支持: openai, doubao, deepseek")
                    sys.exit(1)
            else:
                print(f"错误：--model-provider 需要一个参数")
                sys.exit(1)
        else:
            print(f"未知选项: {arg}")
            print("使用 --help 查看帮助信息")
            sys.exit(1)

        i += 1

    # 调用main函数，传递参数
    main(ai_provider_override=ai_provider)
