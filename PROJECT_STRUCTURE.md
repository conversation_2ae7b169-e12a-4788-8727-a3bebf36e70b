# TPTP双向转换系统 - 项目结构

## 📁 项目目录结构

```
NL2TPTP/
├── 📋 核心程序文件
│   ├── tptp_to_nl.py              # 第一步：TPTP → 自然语言转换主程序
│   ├── nl_to_tptp.py              # 第二步：自然语言 → TPTP转换主程序
│   ├── complete_workflow.py       # 完整双向转换工作流程
│   └── test_eprover.py           # EProver配置测试程序
│
├── 🔧 核心组件类
│   ├── tptp_processor.py          # TPTP文件处理器（第一步核心）
│   ├── nl_to_tptp_processor.py    # 自然语言到TPTP处理器（第二步核心）
│   ├── eprover_validator.py       # EProver验证器
│   ├── chatgpt_client.py          # ChatGPT API客户端
│   ├── file_processor.py          # 通用文件处理器
│   └── config.py                  # 配置管理
│
├── 🛠️ 工具和依赖
│   ├── prover/
│   │   └── eprover                # EProver可执行文件
│   └── requirements.txt           # Python依赖包
│
├── 📂 数据目录
│   ├── tptp/                      # 输入：原始TPTP文件目录
│   │   ├── *.p                    # TPTP问题文件
│   │   └── Axioms/                # 公理文件目录
│   │       └── *.ax               # 公理文件
│   ├── nl/                        # 中间：自然语言文件目录
│   │   └── *.nl                   # 自然语言文件
│   ├── transform_tptp/            # 输出：GPT转换后的TPTP文件目录
│   │   └── *.p                    # 转换后的TPTP文件
│   └── result/                    # 输出：一致性判定结果目录
│       └── comparison_results.txt # 判定结果比对文件
│
└── 📖 文档
    ├── README.md                  # 项目使用说明
    ├── PROJECT_STRUCTURE.md       # 项目结构说明（本文件）
    ├── TPTP_STEP1_SUMMARY.md     # 第一步完成总结
    └── TPTP_STEP2_SUMMARY.md     # 第二步完成总结
```

## 🔄 工作流程

### 第一步：TPTP → 自然语言
```
./tptp/*.p → ./nl/*.nl
```
- 读取TPTP文件
- 解析include指令，整合公理文件
- 使用ChatGPT转换为自然语言
- 保存到nl目录

### 第二步：自然语言 → TPTP
```
./nl/*.nl → ./transform_tptp/*.p
```
- 读取自然语言文件
- 使用ChatGPT转换为TPTP格式
- 使用EProver验证语法
- 迭代修复直到通过验证
- 保存到transform_tptp目录
- 一致性判定结果保存到result目录

## 🚀 快速使用

### 1. 测试EProver配置
```bash
python test_eprover.py
```

### 2. 执行第一步转换
```bash
python tptp_to_nl.py
```

### 3. 执行第二步转换
```bash
python nl_to_tptp.py
```

### 4. 执行完整工作流程
```bash
python complete_workflow.py
```

## ⚙️ 配置说明

### EProver配置
- **路径**: `./prover/eprover`
- **用途**: 验证TPTP语法正确性
- **配置**: 在`config.py`中的`EPROVER_PATH`

### OpenAI API配置
- **环境变量**: `OPENAI_API_KEY`
- **或修改**: `config.py`中的`OPENAI_API_KEY`

### 目录配置
- **TPTP输入**: `./tptp/`
- **自然语言中间**: `./nl/`
- **TPTP输出**: `./transform_tptp/`
- **结果输出**: `./result/`

## 🔧 核心类说明

### TPTPProcessor
- **文件**: `tptp_processor.py`
- **功能**: 处理TPTP文件，解析include指令，调用ChatGPT转换

### NLToTPTPProcessor
- **文件**: `nl_to_tptp_processor.py`
- **功能**: 处理自然语言文件，调用ChatGPT转换，集成EProver验证

### EProverValidator
- **文件**: `eprover_validator.py`
- **功能**: 调用EProver验证TPTP语法，解析错误信息

### ChatGPTClient
- **文件**: `chatgpt_client.py`
- **功能**: 封装OpenAI API调用，支持重试和错误处理

## 📝 文件命名规则

### 输入文件
- TPTP文件: `*.p` (如 `AGT001+1.p`)
- 公理文件: `*.ax` (如 `AGT001+1.ax`)

### 输出文件
- 自然语言: `*.nl` (如 `AGT001+1.nl`)
- 转换TPTP: `*.p` (如 `AGT001+1.p`)

### 文件名一致性
```
AGT001+1.p → AGT001+1.nl → AGT001+1.p
```

## 🛠️ 依赖要求

### Python包
```
openai>=1.0.0
chardet>=5.0.0
pathlib2>=2.3.0
```

### 外部工具
- **EProver**: 已包含在`./prover/eprover`
- **OpenAI API**: 需要有效的API密钥

## 🔍 故障排除

### EProver问题
1. 运行测试: `python test_eprover.py`
2. 检查文件权限: `ls -la prover/eprover`
3. 确保可执行: `chmod +x prover/eprover`

### API问题
1. 检查API密钥设置
2. 验证网络连接
3. 查看日志输出

### 文件问题
1. 确保输入目录存在
2. 检查文件格式和编码
3. 查看处理日志

## 📊 项目统计

- **核心文件**: 10个Python文件
- **支持格式**: TPTP (.p), 公理 (.ax), 自然语言 (.nl)
- **主要功能**: 双向转换、语法验证、错误修复
- **外部依赖**: OpenAI API, EProver
