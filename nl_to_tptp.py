"""
自然语言到TPTP转换主程序
实现从自然语言到TPTP子句的转换工作流程
"""

import os
import sys
from pathlib import Path
from ai_client import AIClient, ChatGPTClient
from nl_to_tptp_processor import NLToTPTPProcessor
from eprover_validator import EProverValidator
from config import Config


def main(enable_preprocessing_override=None, max_fix_attempts_override=None, ai_provider_override=None):
    """主函数 - 执行自然语言到TPTP的转换"""

    print("=" * 60)
    print("自然语言到TPTP转换工具")
    print("=" * 60)

    # 获取配置
    ai_config = Config.get_ai_config()
    nl_config = Config.get_nl_to_tptp_config()

    # 如果有命令行参数覆盖，使用覆盖值
    if enable_preprocessing_override is not None:
        nl_config["enable_nl_preprocessing"] = enable_preprocessing_override
    if max_fix_attempts_override is not None:
        nl_config["max_fix_attempts"] = max_fix_attempts_override
    if ai_provider_override is not None:
        ai_config["provider"] = ai_provider_override
        # 临时设置环境变量以获取正确的配置
        original_provider = os.environ.get("AI_PROVIDER")
        os.environ["AI_PROVIDER"] = ai_provider_override
        ai_config = Config.get_ai_config()
        # 恢复原始环境变量
        if original_provider is not None:
            os.environ["AI_PROVIDER"] = original_provider
        else:
            os.environ.pop("AI_PROVIDER", None)

    # 验证配置（传递模型提供商覆盖参数）
    config_errors = Config.validate_config(ai_provider_override)
    if config_errors:
        print("配置验证失败:")
        for error in config_errors:
            print(f"  - {error}")
        print("\n请检查配置后重试。")
        return
    
    try:
        # 检查eprover是否可用
        print("检查EProver可用性...")
        eprover_validator = EProverValidator(eprover_path=nl_config["eprover_path"])
        
        if not eprover_validator.is_available():
            print("❌ EProver不可用！")
            print("请确保已安装eprover并且在PATH中可访问。")
            print("安装方法:")
            print("  - Ubuntu/Debian: sudo apt-get install eprover")
            print("  - macOS: brew install eprover")
            print("  - 或从官网下载: http://www.eprover.org/")
            return
        
        version = eprover_validator.get_version()
        print(f"✓ EProver可用: {version}")
        
        # 初始化AI客户端
        provider_name = ai_config["provider"].upper()
        print(f"\n初始化{provider_name}客户端...")
        ai_client = AIClient.create_from_config(ai_config)

        # 显示模型信息
        model_info = ai_client.get_model_info()
        print(f"✓ 使用模型: {model_info['provider'].upper()} - {model_info['model']}")
        
        # 初始化自然语言到TPTP处理器
        print("初始化自然语言到TPTP处理器...")
        nl_processor = NLToTPTPProcessor(
            nl_dir=nl_config["nl_dir"],
            transform_tptp_dir=nl_config["transform_tptp_dir"],
            result_dir=nl_config["result_dir"],
            chatgpt_client=ai_client,
            eprover_validator=eprover_validator,
            max_fix_attempts=nl_config["max_fix_attempts"],
            enable_nl_preprocessing=nl_config["enable_nl_preprocessing"],
            enable_validation_classification=nl_config["enable_validation_classification"],
            preprocessed_nl_dir=nl_config.get("preprocessed_nl_dir"),
            fix_steps_dir=nl_config.get("fix_steps_dir")
        )
        
        # 获取处理统计信息
        stats = nl_processor.get_processing_stats()
        print(f"\n处理统计:")
        print(f"  - 自然语言文件数量: {stats['nl_files']}")
        print(f"  - 已有转换TPTP文件: {stats['transform_tptp_files']}")
        print(f"  - 已有结果文件: {stats['result_files']}")

        # 显示验证分类统计
        if stats['valid_problems'] > 0 or stats['invalid_problems'] > 0 or stats['error_problems'] > 0:
            print(f"  - 验证通过问题: {stats['valid_problems']}")
            print(f"  - 验证失败问题: {stats['invalid_problems']}")
            print(f"  - 处理错误问题: {stats['error_problems']}")

        # 显示预处理状态
        if nl_config["enable_nl_preprocessing"]:
            print("  - 自然语言预处理: ✓ 启用")
        else:
            print("  - 自然语言预处理: ✗ 禁用")

        # 显示验证分类状态
        if nl_config["enable_validation_classification"]:
            print("  - 验证结果分类: ✓ 启用")
        else:
            print("  - 验证结果分类: ✗ 禁用")
        
        if stats['nl_files'] == 0:
            print(f"\n在目录 ./nl 中未找到.nl文件")
            print("请先运行第一步生成自然语言文件：python tptp_to_nl.py")
            return
        
        # 询问用户是否继续
        if stats['transform_tptp_files'] > 0:
            response = input(f"\n检测到 {stats['transform_tptp_files']} 个已存在的转换TPTP文件。是否继续处理？(y/n): ")
            if response.lower() != 'y':
                print("操作已取消。")
                return
        
        print(f"\n开始处理 {stats['nl_files']} 个自然语言文件...")
        print("-" * 50)
        
        # 处理所有自然语言文件
        results = nl_processor.process_all_nl_files()
        
        # 统计结果
        successful = sum(1 for success in results.values() if success)
        failed = len(results) - successful
        
        print("\n" + "=" * 50)
        print("处理完成！")
        print(f"成功处理: {successful} 个文件")
        print(f"处理失败: {failed} 个文件")
        
        if failed > 0:
            print("\n失败的文件:")
            for filename, success in results.items():
                if not success:
                    print(f"  - {filename}")
        
        print(f"\n输出目录:")
        print(f"  - TPTP文件: ./transform_tptp")
        print(f"  - 结果文件: ./result")

        # 显示输出文件列表
        transform_files = list(Path("./transform_tptp").glob("*.p"))
        result_files = list(Path("./result").glob("*.txt"))
        if transform_files:
            print(f"\n生成的TPTP文件:")
            for tptp_file in sorted(transform_files):
                print(f"  - {tptp_file.name}")

        if result_files:
            print(f"\n生成的结果文件:")
            for result_file in sorted(result_files):
                print(f"  - {result_file.name}")
        
    except KeyboardInterrupt:
        print("\n\n操作被用户中断。")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()


def test_eprover():
    """测试eprover功能"""
    print("测试EProver功能")
    print("=" * 40)

    nl_config = Config.get_nl_to_tptp_config()
    validator = EProverValidator(eprover_path=nl_config["eprover_path"])
    
    if not validator.is_available():
        print("❌ EProver不可用")
        return
    
    print(f"✓ EProver版本: {validator.get_version()}")
    
    # 测试有效的TPTP内容
    valid_tptp = """
% Test problem
fof(test_axiom, axiom, ![X]: (human(X) => mortal(X))).
fof(socrates_human, axiom, human(socrates)).
fof(prove_mortal, conjecture, mortal(socrates)).
"""
    
    print("\n测试有效的TPTP内容...")
    is_valid, stdout, stderr = validator.validate_tptp_content(valid_tptp, "test_valid.p")
    print(f"验证结果: {'通过' if is_valid else '失败'}")
    if stdout:
        print(f"标准输出: {stdout[:200]}...")
    if stderr:
        print(f"错误输出: {stderr[:200]}...")
    
    # 测试无效的TPTP内容
    invalid_tptp = """
% Test problem with syntax error
fof(test_axiom, axiom, ![X]: (human(X) => mortal(X)).  % Missing closing parenthesis
fof(socrates_human, axiom, human(socrates)).
"""
    
    print("\n测试无效的TPTP内容...")
    is_valid, stdout, stderr = validator.validate_tptp_content(invalid_tptp, "test_invalid.p")
    print(f"验证结果: {'通过' if is_valid else '失败'}")
    if not is_valid:
        error_msg = validator.extract_error_message(stdout, stderr)
        print(f"错误信息: {error_msg}")


def show_help():
    """显示帮助信息"""
    
    help_text = """
自然语言到TPTP转换工具使用说明

用法:
    python nl_to_tptp.py [选项]

选项:
    --help, -h              显示此帮助信息
    --test-eprover          测试eprover功能
    --stats                 仅显示统计信息，不进行转换
    --enable-preprocessing  启用自然语言预处理（简化和去歧义）
    --max-fix-attempts N    设置最大修复尝试次数（默认5次）
    --model-provider NAME   设置AI模型提供商（openai/doubao/deepseek，默认openai）

前置条件:
    1. 已安装eprover并在PATH中可访问
    2. 已设置OpenAI API密钥
    3. nl目录中存在.nl文件（通过第一步生成）

目录结构:
    ./nl/               自然语言文件目录（.nl文件）
    ./transform_tptp/   转换后TPTP文件目录（.p文件）

工作流程:
    1. 读取nl目录中的.nl文件
    2. 使用ChatGPT将自然语言转换为TPTP格式
    3. 使用eprover验证TPTP语法
    4. 如果有错误，反馈给ChatGPT修复
    5. 重复验证直到无错误
    6. 保存到transform_tptp目录

示例:
    # 测试eprover
    python nl_to_tptp.py --test-eprover
    
    # 执行转换
    python nl_to_tptp.py
    
    # 查看统计信息
    python nl_to_tptp.py --stats

注意事项:
    - 确保eprover已正确安装
    - 设置有效的OpenAI API密钥
    - 转换过程可能需要多次迭代修复
"""
    print(help_text)


if __name__ == "__main__":
    # 检查命令行参数
    enable_preprocessing = False
    max_fix_attempts = None
    ai_provider = None

    i = 1
    while i < len(sys.argv):
        arg = sys.argv[i]

        if arg in ["--help", "-h"]:
            show_help()
            sys.exit(0)
        elif arg == "--test-eprover":
            test_eprover()
            sys.exit(0)
        elif arg == "--stats":
            processor = NLToTPTPProcessor()
            stats = processor.get_processing_stats()
            print("处理统计:")
            print(f"  - 自然语言文件数量: {stats['nl_files']}")
            print(f"  - 已有转换TPTP文件: {stats['transform_tptp_files']}")
            sys.exit(0)
        elif arg == "--enable-preprocessing":
            enable_preprocessing = True
            print("✓ 启用自然语言预处理")
        elif arg == "--max-fix-attempts":
            if i + 1 < len(sys.argv):
                try:
                    max_fix_attempts = int(sys.argv[i + 1])
                    print(f"✓ 设置最大修复尝试次数: {max_fix_attempts}")
                    i += 1  # 跳过下一个参数
                except ValueError:
                    print(f"错误：--max-fix-attempts 需要一个整数参数")
                    sys.exit(1)
            else:
                print(f"错误：--max-fix-attempts 需要一个参数")
                sys.exit(1)
        elif arg == "--model-provider":
            if i + 1 < len(sys.argv):
                provider = sys.argv[i + 1].lower()
                if provider in ["openai", "doubao", "deepseek"]:
                    ai_provider = provider
                    print(f"✓ 设置AI模型提供商: {provider.upper()}")
                    i += 1  # 跳过下一个参数
                else:
                    print(f"错误：不支持的模型提供商 '{provider}'，支持: openai, doubao, deepseek")
                    sys.exit(1)
            else:
                print(f"错误：--model-provider 需要一个参数")
                sys.exit(1)
        else:
            print(f"未知选项: {arg}")
            print("使用 --help 查看帮助信息")
            sys.exit(1)

        i += 1

    # 调用main函数，传递参数
    main(enable_preprocessing_override=enable_preprocessing,
         max_fix_attempts_override=max_fix_attempts,
         ai_provider_override=ai_provider)
