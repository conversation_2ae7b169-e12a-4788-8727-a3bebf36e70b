# TPTP双向转换系统操作说明

## 📋 系统概述

TPTP双向转换系统是一个基于AI的逻辑公式转换工具，支持TPTP格式与自然语言之间的双向转换。系统集成了多种AI模型，提供智能错误修复、验证分类等高级功能。

## 🚀 快速开始

### 1. 环境准备

#### 必需组件
- Python 3.8+
- EProver（已包含在`prover/`目录）
- AI模型API密钥（OpenAI或字节跳动）

#### API密钥配置
```bash
# OpenAI模型
export OPENAI_API_KEY="your-openai-api-key"

# 字节跳动模型（Doubao/DeepSeek）
export ARK_API_KEY="your-bytedance-api-key"
```

### 2. 目录结构
```
NL2TPTP/
├── tptp/              # 输入：原始TPTP文件
├── nl/                # 中间：自然语言文件
├── transform_tptp/    # 输出：转换后TPTP文件
├── result/            # 结果：验证和比较结果
└── prover/            # EProver工具
```

## 🔧 基本操作

### 方式一：完整工作流程（推荐）

执行完整的双向转换：
```bash
# 使用默认配置
python complete_workflow.py

# 使用指定AI模型
python complete_workflow.py --model-provider deepseek

# 启用所有高级功能
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5
```

### 方式二：分步执行

#### 第一步：TPTP → 自然语言
```bash
# 基本转换
python tptp_to_nl.py

# 指定AI模型
python tptp_to_nl.py --model-provider openai
python tptp_to_nl.py --model-provider doubao
python tptp_to_nl.py --model-provider deepseek
```

#### 第二步：自然语言 → TPTP
```bash
# 基本转换
python nl_to_tptp.py

# 启用预处理和修复
python nl_to_tptp.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 3
```

## 🤖 AI模型选择

### OpenAI模型
```bash
# 使用GPT-3.5（快速、经济）
python complete_workflow.py --model-provider openai

# 环境变量配置
export AI_PROVIDER=openai
export OPENAI_MODEL=gpt-3.5-turbo
```

### Doubao模型
```bash
# 使用Doubao（中文优化）
python complete_workflow.py --model-provider doubao

# 环境变量配置
export AI_PROVIDER=doubao
export DOUBAO_MODEL=doubao-seed-1-6-250615
```

### DeepSeek R1模型
```bash
# 使用DeepSeek R1（推理优化）
python complete_workflow.py --model-provider deepseek

# 环境变量配置
export AI_PROVIDER=deepseek
export DEEPSEEK_MODEL=deepseek-r1-250528
```

## ⚙️ 高级功能

### 自然语言预处理
自动简化和去歧义自然语言描述：
```bash
python nl_to_tptp.py --enable-preprocessing
```

**效果示例：**
- 原文："在某些情况下，如果一个集合包含另一个集合的所有元素..."
- 简化："定义：如果集合A包含集合B的所有元素，那么A是B的超集"

### 智能错误修复
GPT自动修复TPTP语法错误：
```bash
python nl_to_tptp.py --max-fix-attempts 5
```

**修复类型：**
- 函数参数数量错误
- 括号不匹配
- 量词作用域问题
- 自由变量错误

### 验证结果分类
自动分类验证结果：
- **VALID**：语法正确，可被EProver处理
- **INVALID**：存在语法错误
- **ERROR**：处理过程出现异常

结果保存在`result/`目录：
- `valid_problems.txt` - 验证通过的问题
- `invalid_problems.txt` - 验证失败的问题
- `validation_summary.txt` - 验证汇总

## 📊 结果查看

### 统计信息
```bash
# 查看处理统计
python tptp_to_nl.py --stats
python nl_to_tptp.py --stats
```

### 验证结果
```bash
# 查看验证分类结果
cat result/validation_summary.txt

# 查看一致性比较
cat result/comparison_results.txt
```

### 生成的文件
```bash
# 查看生成的自然语言文件
ls -la nl/

# 查看转换后的TPTP文件
ls -la transform_tptp/

# 查看验证和比较结果
ls -la result/
```

## 🛠️ 常用命令组合

### 开发测试
```bash
# 快速测试（少量修复尝试）
python complete_workflow.py \
  --model-provider openai \
  --max-fix-attempts 2

# 查看统计信息
python tptp_to_nl.py --stats
python nl_to_tptp.py --stats
```

### 生产环境
```bash
# 高质量转换（启用所有功能）
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5

# 批量处理多个文件
for model in openai doubao deepseek; do
  echo "使用 $model 模型..."
  python complete_workflow.py --model-provider $model
done
```

### 质量分析
```bash
# 对比不同模型的效果
python complete_workflow.py --model-provider openai
mv result/validation_summary.txt result/openai_results.txt

python complete_workflow.py --model-provider deepseek  
mv result/validation_summary.txt result/deepseek_results.txt

# 分析结果差异
diff result/openai_results.txt result/deepseek_results.txt
```

## 🔍 故障排除

### 常见问题

#### 1. API密钥错误
```bash
# 检查密钥设置
echo $OPENAI_API_KEY
echo $ARK_API_KEY

# 测试连接
python tptp_to_nl.py --model-provider openai --stats
```

#### 2. EProver路径问题
```bash
# 检查EProver
./prover/eprover --version

# 如果权限问题
chmod +x prover/eprover
```

#### 3. 文件编码问题
```bash
# 检查文件编码
file tptp/*.p
file nl/*.nl

# 转换编码（如需要）
iconv -f ISO-8859-1 -t UTF-8 input.p > output.p
```

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
# 然后运行你的命令
"
```

## 📈 性能优化

### 模型选择建议
- **快速测试**：OpenAI gpt-3.5-turbo
- **中文内容**：Doubao或DeepSeek R1
- **复杂逻辑**：DeepSeek R1或OpenAI gpt-4
- **批量处理**：考虑成本，选择Doubao或DeepSeek R1

### 参数调优
```bash
# 平衡质量和速度
python complete_workflow.py \
  --model-provider deepseek \
  --max-fix-attempts 3

# 最高质量（较慢）
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 10
```

## 📝 最佳实践

### 1. 文件准备
- 确保TPTP文件格式正确
- 使用UTF-8编码
- 文件名使用英文和数字

### 2. 模型选择
- 中文逻辑问题：优先使用DeepSeek R1
- 英文标准问题：可使用OpenAI
- 成本敏感场景：使用Doubao

### 3. 参数配置
- 开发阶段：`--max-fix-attempts 2`
- 生产环境：`--max-fix-attempts 5`
- 复杂问题：启用`--enable-preprocessing`

### 4. 结果验证
- 检查`result/validation_summary.txt`
- 对比原始和转换后的文件大小
- 验证逻辑一致性

## 🎯 使用场景

### 学术研究
```bash
# 高质量转换，详细分析
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5
```

### 工业应用
```bash
# 批量处理，平衡效率
python complete_workflow.py \
  --model-provider doubao \
  --max-fix-attempts 3
```

### 教学演示
```bash
# 分步展示，便于理解
python tptp_to_nl.py --model-provider openai
python nl_to_tptp.py --model-provider openai --enable-preprocessing
```

## 📋 完整操作流程示例

### 示例1：首次使用完整流程
```bash
# 1. 设置API密钥
export OPENAI_API_KEY="your-openai-api-key"

# 2. 准备TPTP文件（放入tptp/目录）
ls tptp/

# 3. 执行完整转换
python complete_workflow.py --model-provider openai

# 4. 查看结果
ls nl/              # 自然语言文件
ls transform_tptp/  # 转换后TPTP文件
cat result/validation_summary.txt  # 验证结果
```

### 示例2：高质量转换流程
```bash
# 1. 使用DeepSeek R1模型（推理能力强）
export ARK_API_KEY="your-bytedance-api-key"

# 2. 启用所有高级功能
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5

# 3. 分析结果质量
grep "VALID" result/validation_summary.txt | wc -l    # 成功数量
grep "INVALID" result/validation_summary.txt | wc -l  # 失败数量
```

### 示例3：批量对比不同模型
```bash
#!/bin/bash
# 对比脚本：compare_models.sh

models=("openai" "doubao" "deepseek")

for model in "${models[@]}"; do
    echo "=== 测试模型: $model ==="

    # 清理之前的结果
    rm -f result/validation_summary.txt

    # 运行转换
    python complete_workflow.py --model-provider $model

    # 保存结果
    cp result/validation_summary.txt result/${model}_results.txt

    # 统计成功率
    total=$(wc -l < result/${model}_results.txt)
    valid=$(grep -c "VALID" result/${model}_results.txt)
    echo "模型 $model: 成功率 $valid/$total"
done
```

## 🔧 高级配置

### 环境变量完整配置
```bash
# 创建配置文件：.env
cat > .env << EOF
# AI模型配置
AI_PROVIDER=deepseek
OPENAI_API_KEY=your-openai-key
ARK_API_KEY=your-bytedance-key

# 模型参数
OPENAI_MODEL=gpt-4
DOUBAO_MODEL=doubao-seed-1-6-250615
DEEPSEEK_MODEL=deepseek-r1-250528

# 功能开关
ENABLE_NL_PREPROCESSING=true
MAX_FIX_ATTEMPTS=5

# EProver配置
EPROVER_TIMEOUT=30
EOF

# 加载配置
source .env
```

### 自定义配置文件
```python
# 修改config.py中的默认值
class Config:
    # 自定义默认AI提供商
    AI_PROVIDER = "deepseek"

    # 自定义默认参数
    DEFAULT_TEMPERATURE = 0.3
    DEFAULT_MAX_TOKENS = 4000
    MAX_FIX_ATTEMPTS = 10

    # 启用预处理
    ENABLE_NL_PREPROCESSING = True
```

## 📊 结果分析工具

### 创建分析脚本
```bash
# 创建结果分析脚本：analyze_results.py
cat > analyze_results.py << 'EOF'
#!/usr/bin/env python3
import pandas as pd
from pathlib import Path

def analyze_results():
    result_dir = Path("result")

    # 读取验证结果
    if (result_dir / "validation_summary.txt").exists():
        with open(result_dir / "validation_summary.txt", 'r') as f:
            lines = f.readlines()

        valid_count = sum(1 for line in lines if "VALID" in line)
        invalid_count = sum(1 for line in lines if "INVALID" in line)
        total = len(lines)

        print(f"验证结果分析:")
        print(f"  总计: {total}")
        print(f"  成功: {valid_count} ({valid_count/total*100:.1f}%)")
        print(f"  失败: {invalid_count} ({invalid_count/total*100:.1f}%)")

    # 分析文件大小
    tptp_dir = Path("tptp")
    transform_dir = Path("transform_tptp")

    if tptp_dir.exists() and transform_dir.exists():
        print(f"\n文件大小分析:")
        for tptp_file in tptp_dir.glob("*.p"):
            transform_file = transform_dir / tptp_file.name
            if transform_file.exists():
                orig_size = tptp_file.stat().st_size
                trans_size = transform_file.stat().st_size
                ratio = trans_size / orig_size
                print(f"  {tptp_file.name}: {orig_size} → {trans_size} ({ratio:.2f}x)")

if __name__ == "__main__":
    analyze_results()
EOF

chmod +x analyze_results.py
```

### 使用分析工具
```bash
# 运行分析
python analyze_results.py

# 生成详细报告
python analyze_results.py > analysis_report.txt
```

## 🚀 自动化脚本

### 创建一键运行脚本
```bash
# 创建自动化脚本：auto_convert.sh
cat > auto_convert.sh << 'EOF'
#!/bin/bash

# TPTP双向转换自动化脚本

set -e  # 遇到错误立即退出

echo "🚀 TPTP双向转换自动化脚本"
echo "================================"

# 检查API密钥
if [ -z "$OPENAI_API_KEY" ] && [ -z "$ARK_API_KEY" ]; then
    echo "❌ 错误: 请设置API密钥"
    echo "export OPENAI_API_KEY='your-key' 或 export ARK_API_KEY='your-key'"
    exit 1
fi

# 检查输入文件
if [ ! -d "tptp" ] || [ -z "$(ls -A tptp/*.p 2>/dev/null)" ]; then
    echo "❌ 错误: tptp目录中没有.p文件"
    exit 1
fi

# 选择模型
echo "请选择AI模型:"
echo "1) OpenAI GPT"
echo "2) 字节跳动 Doubao"
echo "3) 字节跳动 DeepSeek R1"
read -p "请输入选择 (1-3): " choice

case $choice in
    1) MODEL="openai" ;;
    2) MODEL="doubao" ;;
    3) MODEL="deepseek" ;;
    *) echo "无效选择"; exit 1 ;;
esac

# 询问是否启用高级功能
read -p "是否启用自然语言预处理? (y/n): " preprocessing
read -p "设置最大修复尝试次数 (默认5): " attempts

# 设置参数
ARGS="--model-provider $MODEL"
if [ "$preprocessing" = "y" ]; then
    ARGS="$ARGS --enable-preprocessing"
fi
if [ ! -z "$attempts" ]; then
    ARGS="$ARGS --max-fix-attempts $attempts"
fi

echo "🔄 开始转换..."
echo "使用参数: $ARGS"

# 执行转换
python complete_workflow.py $ARGS

# 显示结果
echo "✅ 转换完成!"
echo "📊 结果统计:"
python analyze_results.py

echo "📁 生成的文件:"
echo "  - 自然语言: nl/"
echo "  - 转换TPTP: transform_tptp/"
echo "  - 验证结果: result/"
EOF

chmod +x auto_convert.sh
```

### 使用自动化脚本
```bash
# 运行自动化脚本
./auto_convert.sh
```

---

## 📞 技术支持

### 常见错误解决

#### API相关错误
```bash
# 测试API连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# 测试字节跳动API
curl -H "Authorization: Bearer $ARK_API_KEY" \
     https://ark.cn-beijing.volces.com/api/v3/models
```

#### 文件权限错误
```bash
# 修复权限
chmod +x prover/eprover
chmod -R 755 .
```

#### Python依赖错误
```bash
# 安装依赖
pip install openai pathlib logging
```

### 获取帮助
```bash
# 查看程序帮助
python complete_workflow.py --help
python tptp_to_nl.py --help
python nl_to_tptp.py --help

# 查看详细文档
cat MODEL_SWITCHING_GUIDE.md
cat README.md
```

### 联系支持
如遇问题，请提供：
1. 错误信息截图
2. 使用的命令
3. 系统环境信息
4. API密钥状态（脱敏）

更多资源：
- 📖 `MODEL_SWITCHING_GUIDE.md` - 模型切换详细指南
- 📋 `README.md` - 系统总体介绍
- 🔧 各程序的`--help`选项
