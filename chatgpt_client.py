"""
ChatGPT API 客户端类
负责调用 OpenAI ChatGPT 接口，根据输入获取输出
"""

import openai
import time
import logging
from typing import Optional, Dict, Any
import json


class ChatGPTClient:
    """ChatGPT API 客户端类"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", 
                 base_url: Optional[str] = None, max_retries: int = 3):
        """
        初始化 ChatGPT 客户端
        
        Args:
            api_key (str): OpenAI API 密钥
            model (str): 使用的模型名称，默认为 gpt-3.5-turbo
            base_url (str, optional): API 基础URL，用于自定义端点
            max_retries (int): 最大重试次数
        """
        self.api_key = api_key
        self.model = model
        self.max_retries = max_retries
        
        # 配置 OpenAI 客户端
        if base_url:
            self.client = openai.OpenAI(api_key=api_key, base_url=base_url)
        else:
            self.client = openai.OpenAI(api_key=api_key)
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def get_response(self, prompt: str, system_message: Optional[str] = None,
                    temperature: float = 0.7, max_tokens: Optional[int] = None) -> str:
        """
        获取 ChatGPT 响应
        
        Args:
            prompt (str): 用户输入的提示词
            system_message (str, optional): 系统消息
            temperature (float): 温度参数，控制输出的随机性
            max_tokens (int, optional): 最大输出token数
            
        Returns:
            str: ChatGPT 的响应内容
            
        Raises:
            Exception: 当API调用失败时抛出异常
        """
        messages = []
        
        # 添加系统消息
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        # 添加用户消息
        messages.append({"role": "user", "content": prompt})
        
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"正在调用 ChatGPT API (尝试 {attempt + 1}/{self.max_retries})")
                
                # 构建请求参数
                request_params = {
                    "model": self.model,
                    "messages": messages,
                    "temperature": temperature
                }
                
                if max_tokens:
                    request_params["max_tokens"] = max_tokens
                
                # 调用 API
                response = self.client.chat.completions.create(**request_params)
                
                # 提取响应内容
                content = response.choices[0].message.content
                
                self.logger.info("API 调用成功")
                return content
                
            except openai.RateLimitError as e:
                self.logger.warning(f"速率限制错误: {e}")
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise Exception(f"达到最大重试次数，API调用失败: {e}")
                    
            except openai.APIError as e:
                self.logger.error(f"API 错误: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
                else:
                    raise Exception(f"API调用失败: {e}")
                    
            except Exception as e:
                self.logger.error(f"未知错误: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
                else:
                    raise Exception(f"API调用失败: {e}")
    
    def get_response_with_context(self, messages: list, temperature: float = 0.7,
                                max_tokens: Optional[int] = None) -> str:
        """
        使用完整对话上下文获取响应
        
        Args:
            messages (list): 完整的对话消息列表
            temperature (float): 温度参数
            max_tokens (int, optional): 最大输出token数
            
        Returns:
            str: ChatGPT 的响应内容
        """
        for attempt in range(self.max_retries):
            try:
                request_params = {
                    "model": self.model,
                    "messages": messages,
                    "temperature": temperature
                }
                
                if max_tokens:
                    request_params["max_tokens"] = max_tokens
                
                response = self.client.chat.completions.create(**request_params)
                return response.choices[0].message.content
                
            except Exception as e:
                self.logger.error(f"API调用失败 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise Exception(f"API调用失败: {e}")
    
    def estimate_tokens(self, text: str) -> int:
        """
        估算文本的token数量（粗略估算）
        
        Args:
            text (str): 要估算的文本
            
        Returns:
            int: 估算的token数量
        """
        # 粗略估算：英文约4个字符=1个token，中文约1.5个字符=1个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars // 1.5 + other_chars // 4
        return int(estimated_tokens)
