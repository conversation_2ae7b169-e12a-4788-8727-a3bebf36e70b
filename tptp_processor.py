"""
TPTP处理器类
专门用于处理TPTP文件格式，实现TPTP子句到自然语言的转换
"""

import os
import re
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from ai_client import AIClient
# 保持向后兼容
ChatGPTClient = AIClient
from file_processor import FileProcessor


class TPTPProcessor:
    """TPTP文件处理器"""
    
    def __init__(self, tptp_dir: str = "./tptp", nl_dir: str = "./nl",
                 chatgpt_client: Optional[AIClient] = None):
        """
        初始化TPTP处理器
        
        Args:
            tptp_dir (str): TPTP文件目录
            nl_dir (str): 自然语言输出目录
            chatgpt_client (AIClient, optional): AI客户端实例（支持OpenAI、Doubao、DeepSeek）
        """
        self.tptp_dir = Path(tptp_dir)
        self.nl_dir = Path(nl_dir)
        self.chatgpt_client = chatgpt_client
        
        # 创建输出目录
        self.nl_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # include指令的正则表达式
        self.include_pattern = re.compile(r'include\s*\(\s*[\'"]?([^\'"\)]+)[\'"]?\s*\)')
        
        # 初始化文件处理器
        self.file_processor = FileProcessor(str(self.tptp_dir), str(self.nl_dir))
    
    def process_all_tptp_files(self) -> Dict[str, bool]:
        """
        处理所有TPTP文件，转换为自然语言
        
        Returns:
            Dict[str, bool]: 文件名到处理结果的映射
        """
        results = {}
        
        if not self.tptp_dir.exists():
            self.logger.error(f"TPTP目录不存在: {self.tptp_dir}")
            return results
        
        # 查找所有.p文件
        tptp_files = list(self.tptp_dir.glob("*.p"))
        
        if not tptp_files:
            self.logger.warning(f"在目录 {self.tptp_dir} 中未找到.p文件")
            return results
        
        self.logger.info(f"找到 {len(tptp_files)} 个TPTP文件")
        
        for tptp_file in tptp_files:
            try:
                self.logger.info(f"处理文件: {tptp_file.name}")
                success = self.process_single_tptp_file(tptp_file)
                results[tptp_file.name] = success
                
                if success:
                    self.logger.info(f"✓ 成功处理: {tptp_file.name}")
                else:
                    self.logger.error(f"✗ 处理失败: {tptp_file.name}")
                    
            except Exception as e:
                self.logger.error(f"处理文件 {tptp_file.name} 时出错: {e}")
                results[tptp_file.name] = False
        
        return results
    
    def process_single_tptp_file(self, tptp_file: Path) -> bool:
        """
        处理单个TPTP文件
        
        Args:
            tptp_file (Path): TPTP文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 读取TPTP文件内容
            content = self.file_processor.get_file_content(str(tptp_file))
            if content is None:
                return False
            
            # 解析并整合include文件
            integrated_content = self.integrate_includes(content, tptp_file.parent)
            
            # 转换为自然语言
            if self.chatgpt_client:
                natural_language = self.convert_to_natural_language(integrated_content, tptp_file.name)
                if natural_language is None:
                    return False
            else:
                self.logger.warning("未提供ChatGPT客户端，跳过自然语言转换")
                natural_language = f"原始TPTP内容:\n{integrated_content}"
            
            # 生成输出文件名
            output_filename = tptp_file.stem + ".nl"
            
            # 保存结果
            success = self.file_processor.write_output(
                content=natural_language,
                filename=output_filename
            )
            
            return success
            
        except Exception as e:
            self.logger.error(f"处理文件 {tptp_file} 时出错: {e}")
            return False
    
    def integrate_includes(self, content: str, base_dir: Path) -> str:
        """
        解析并整合include指令引用的文件

        Args:
            content (str): 原始文件内容
            base_dir (Path): 基础目录路径

        Returns:
            str: 整合后的内容（所有include指令都被删除）
        """
        integrated_content = content
        processed_includes = set()  # 防止循环引用

        # 持续处理直到没有include指令为止
        while True:
            # 查找所有include指令
            includes = self.include_pattern.findall(integrated_content)

            if not includes:
                break  # 没有更多include指令，退出循环

            content_changed = False

            for include_path in includes:
                if include_path in processed_includes:
                    # 如果已经处理过但仍然存在，直接删除
                    include_directive = f"include({include_path})"
                    integrated_content = integrated_content.replace(include_directive,
                        f"% REMOVED: 重复的include指令 {include_path}")
                    content_changed = True
                    continue

                processed_includes.add(include_path)

                # 构建完整的文件路径
                full_include_path = base_dir / include_path

                try:
                    # 读取include文件内容
                    include_content = self.file_processor.get_file_content(str(full_include_path))

                    if include_content:
                        self.logger.info(f"成功读取include文件: {include_path}")

                        # 递归处理include文件中的include指令
                        include_content = self.integrate_includes(include_content, full_include_path.parent)

                        # 替换include指令为实际内容
                        include_directive = f"include({include_path})"
                        replacement = f"""
% ========== 开始 include({include_path}) ==========
{include_content}
% ========== 结束 include({include_path}) ==========
"""
                        integrated_content = integrated_content.replace(include_directive, replacement)
                        content_changed = True

                    else:
                        self.logger.error(f"无法读取include文件: {include_path}")
                        # 删除无法读取的include指令并添加注释
                        include_directive = f"include({include_path})"
                        replacement = f"% ERROR: 无法读取文件 {include_path}，已删除include指令"
                        integrated_content = integrated_content.replace(include_directive, replacement)
                        content_changed = True

                except Exception as e:
                    self.logger.error(f"处理include文件 {include_path} 时出错: {e}")
                    # 删除出错的include指令并添加错误注释
                    include_directive = f"include({include_path})"
                    replacement = f"% ERROR: 处理文件 {include_path} 时出错: {e}，已删除include指令"
                    integrated_content = integrated_content.replace(include_directive, replacement)
                    content_changed = True

            if not content_changed:
                break  # 如果没有任何变化，退出循环防止无限循环

        # 最终清理：删除任何剩余的include指令
        final_includes = self.include_pattern.findall(integrated_content)
        for include_path in final_includes:
            include_directive = f"include({include_path})"
            integrated_content = integrated_content.replace(include_directive,
                f"% REMOVED: 剩余的include指令 {include_path}")
            self.logger.warning(f"删除剩余的include指令: {include_path}")

        # 检查内容长度并发出警告
        self._check_content_length(integrated_content)

        return integrated_content

    def _check_content_length(self, content: str):
        """检查内容长度是否可能超出AI模型限制"""
        # 估算token数量（粗略估算：4个字符≈1个token）
        estimated_tokens = len(content) // 4

        # 定义不同模型的安全限制（预留30%给输出）
        model_limits = {
            "gpt-3.5-turbo": 11469,    # 16385 * 0.7
            "gpt-4": 5734,             # 8192 * 0.7
            "gpt-4-turbo": 89600,      # 128000 * 0.7
            "doubao": 22937,           # 32768 * 0.7
            "deepseek": 22937          # 32768 * 0.7
        }

        self.logger.info(f"整合后内容长度: {len(content):,} 字符, 估算 ~{estimated_tokens:,} tokens")

        # 检查是否接近或超出限制
        warnings_issued = False
        for model, limit in model_limits.items():
            usage_percent = (estimated_tokens / limit) * 100

            if estimated_tokens > limit:
                self.logger.error(f"⚠️ 内容可能超出 {model} 的上下文限制 ({usage_percent:.1f}%)")
                warnings_issued = True
            elif usage_percent > 80:
                self.logger.warning(f"⚠️ 内容接近 {model} 的上下文限制 ({usage_percent:.1f}%)")
                warnings_issued = True

        if warnings_issued:
            self.logger.warning("建议考虑以下解决方案:")
            self.logger.warning("  1. 使用更大上下文的模型（如GPT-4-turbo）")
            self.logger.warning("  2. 简化公理文件内容")
            self.logger.warning("  3. 选择性包含相关公理")
        else:
            self.logger.info("✓ 内容长度在所有模型的安全范围内")
    
    def convert_to_natural_language(self, tptp_content: str, filename: str) -> Optional[str]:
        """
        使用ChatGPT将TPTP内容转换为自然语言
        
        Args:
            tptp_content (str): TPTP文件内容
            filename (str): 文件名
            
        Returns:
            Optional[str]: 转换后的自然语言，失败时返回None
        """
        if not self.chatgpt_client:
            self.logger.error("ChatGPT客户端未初始化")
            return None
        
        try:
            # 构建提示词
            prompt = self.build_tptp_conversion_prompt(tptp_content, filename)
            
            # 调用ChatGPT
            response = self.chatgpt_client.get_response(
                prompt=prompt,
                system_message=self.get_tptp_system_message(),
                temperature=0.3,  # 使用较低的温度以获得更一致的结果
                max_tokens=4000   # 限制输出长度
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"转换文件 {filename} 时出错: {e}")
            return None
    
    def build_tptp_conversion_prompt(self, tptp_content: str, filename: str) -> str:
        """
        构建TPTP转换的提示词
        
        Args:
            tptp_content (str): TPTP内容
            filename (str): 文件名
            
        Returns:
            str: 构建的提示词
        """
        prompt = f"""
请将以下TPTP（Thousands of Problems for Theorem Provers）格式的逻辑公式转换为清晰的自然语言描述。

文件名: {filename}

TPTP内容:
{tptp_content}

请按照以下要求进行转换：

1. **保持逻辑结构**: 准确表达原始逻辑关系，包括量词、连接词等
2. **使用自然语言**: 将形式化的逻辑表达式转换为易于理解的自然语言
3. **保留重要信息**: 包括公理名称、定理名称、角色（axiom, conjecture等）
4. **组织结构**: 按照公理、定义、定理、猜想等分类组织
5. **添加解释**: 对复杂的逻辑关系提供简要解释

输出格式要求：
- 使用中文进行描述
- 保持清晰的段落结构
- 对每个重要的逻辑语句提供自然语言翻译
- 如果有include的公理文件，请说明其作用

请开始转换：
"""
        return prompt
    
    def get_tptp_system_message(self) -> str:
        """
        获取TPTP转换的系统消息
        
        Returns:
            str: 系统消息
        """
        return """你是一个专业的逻辑学和形式化验证专家，精通TPTP格式和一阶逻辑。你的任务是将TPTP格式的逻辑公式准确地转换为清晰易懂的自然语言描述，同时保持逻辑的严谨性和完整性。

你需要：
1. 准确理解TPTP语法和语义
2. 将形式化逻辑转换为自然语言，但不丢失逻辑精确性
3. 使用清晰的中文表达
4. 保持逻辑结构的层次性和组织性
5. 对复杂概念提供适当的解释"""
    
    def get_processing_stats(self) -> Dict[str, int]:
        """
        获取处理统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        stats = {
            'tptp_files': 0,
            'nl_files': 0,
            'axiom_files': 0
        }
        
        if self.tptp_dir.exists():
            stats['tptp_files'] = len(list(self.tptp_dir.glob("*.p")))
            stats['axiom_files'] = len(list(self.tptp_dir.rglob("*.ax")))
        
        if self.nl_dir.exists():
            stats['nl_files'] = len(list(self.nl_dir.glob("*.nl")))
        
        return stats
    
    def validate_tptp_syntax(self, content: str) -> Tuple[bool, List[str]]:
        """
        简单的TPTP语法验证
        
        Args:
            content (str): TPTP内容
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查基本的TPTP语法元素
        if not re.search(r'fof\s*\(', content) and not re.search(r'cnf\s*\(', content):
            errors.append("未找到有效的TPTP公式声明（fof或cnf）")
        
        # 检查括号匹配
        open_parens = content.count('(')
        close_parens = content.count(')')
        if open_parens != close_parens:
            errors.append(f"括号不匹配：开括号{open_parens}个，闭括号{close_parens}个")
        
        # 检查基本的角色关键字
        valid_roles = ['axiom', 'hypothesis', 'definition', 'assumption', 'lemma', 
                      'theorem', 'conjecture', 'negated_conjecture', 'plain', 'type']
        
        role_pattern = re.compile(r'(?:fof|cnf)\s*\([^,]+,\s*(\w+)')
        roles_found = role_pattern.findall(content)
        
        for role in roles_found:
            if role not in valid_roles:
                errors.append(f"未知的角色类型: {role}")
        
        return len(errors) == 0, errors
