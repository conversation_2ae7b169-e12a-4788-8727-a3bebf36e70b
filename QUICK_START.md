# TPTP双向转换系统 - 快速入门

## 🚀 5分钟快速开始

### 第一步：设置API密钥
```bash
# 选择一种AI模型并设置对应的API密钥

# 方案1：使用OpenAI（推荐新手）
export OPENAI_API_KEY="your-openai-api-key"

# 方案2：使用字节跳动模型（推荐中文用户）
export ARK_API_KEY="your-bytedance-api-key"
```

### 第二步：准备TPTP文件
```bash
# 将你的TPTP文件放入tptp目录
cp your_problem.p tptp/

# 或者使用系统自带的示例文件
ls tptp/  # 查看现有文件
```

### 第三步：一键转换
```bash
# 使用OpenAI模型
python complete_workflow.py --model-provider openai

# 使用字节跳动DeepSeek R1模型（推理能力强）
python complete_workflow.py --model-provider deepseek

# 启用高级功能（推荐）
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 3
```

### 第四步：查看结果
```bash
# 查看生成的自然语言文件
cat nl/*.nl

# 查看转换后的TPTP文件
cat transform_tptp/*.p

# 查看验证结果
cat result/validation_summary.txt
```

## 🎯 常用命令

### 基础使用
```bash
# 完整转换（推荐）
python complete_workflow.py --model-provider deepseek

# 仅TPTP转自然语言
python tptp_to_nl.py --model-provider openai

# 仅自然语言转TPTP
python nl_to_tptp.py --model-provider doubao
```

### 高质量转换
```bash
# 启用所有功能，获得最佳质量
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5
```

### 快速测试
```bash
# 快速测试（减少修复尝试次数）
python complete_workflow.py \
  --model-provider openai \
  --max-fix-attempts 2
```

## 🤖 AI模型选择指南

| 模型 | 适用场景 | 命令 |
|------|----------|------|
| **OpenAI** | 英文内容、快速测试 | `--model-provider openai` |
| **Doubao** | 中文内容、成本敏感 | `--model-provider doubao` |
| **DeepSeek R1** | 复杂逻辑、数学推理 | `--model-provider deepseek` |

## 📊 结果解读

### 成功标志
- ✅ 看到"🎉 完整工作流程执行成功！"
- ✅ `nl/`目录有自然语言文件
- ✅ `transform_tptp/`目录有转换后的TPTP文件
- ✅ `result/validation_summary.txt`显示VALID结果

### 验证结果含义
- **VALID**: 转换成功，语法正确
- **INVALID**: 转换失败，存在语法错误
- **ERROR**: 处理过程出现异常

### 文件说明
```
生成的文件结构：
├── nl/                    # 自然语言描述文件
├── transform_tptp/        # 转换后的TPTP文件
└── result/
    ├── validation_summary.txt      # 验证结果汇总
    ├── comparison_results.txt      # 一致性比较结果
    ├── valid_problems.txt          # 验证通过的问题
    └── invalid_problems.txt        # 验证失败的问题
```

## 🔧 常见问题

### Q: API密钥错误怎么办？
```bash
# 检查密钥是否设置
echo $OPENAI_API_KEY
echo $ARK_API_KEY

# 重新设置密钥
export OPENAI_API_KEY="your-correct-key"
```

### Q: 没有TPTP文件怎么办？
```bash
# 查看示例文件
ls tptp/

# 如果没有文件，创建示例
python tptp_to_nl.py --setup
```

### Q: 转换失败怎么办？
```bash
# 查看详细错误信息
cat result/validation_summary.txt

# 尝试增加修复次数
python complete_workflow.py \
  --model-provider deepseek \
  --max-fix-attempts 10
```

### Q: 如何提高转换质量？
```bash
# 使用DeepSeek R1模型 + 预处理
python complete_workflow.py \
  --model-provider deepseek \
  --enable-preprocessing \
  --max-fix-attempts 5
```

## 📈 进阶使用

### 批量处理
```bash
# 处理多个文件
for file in tptp/*.p; do
    echo "处理文件: $file"
    python complete_workflow.py --model-provider deepseek
done
```

### 对比不同模型
```bash
# 测试OpenAI
python complete_workflow.py --model-provider openai
cp result/validation_summary.txt result/openai_results.txt

# 测试DeepSeek
python complete_workflow.py --model-provider deepseek  
cp result/validation_summary.txt result/deepseek_results.txt

# 对比结果
diff result/openai_results.txt result/deepseek_results.txt
```

### 自定义配置
```bash
# 设置环境变量
export AI_PROVIDER=deepseek
export MAX_FIX_ATTEMPTS=10
export ENABLE_NL_PREPROCESSING=true

# 使用自定义配置
python complete_workflow.py
```

## 🎓 学习资源

### 详细文档
- 📖 `OPERATION_GUIDE.md` - 完整操作指南
- 🔧 `MODEL_SWITCHING_GUIDE.md` - 模型切换详细说明
- 📋 `README.md` - 系统总体介绍

### 命令帮助
```bash
# 查看所有可用选项
python complete_workflow.py --help
python tptp_to_nl.py --help
python nl_to_tptp.py --help
```

### 示例命令
```bash
# 查看统计信息
python tptp_to_nl.py --stats
python nl_to_tptp.py --stats

# 测试AI连接
python tptp_to_nl.py --model-provider openai --stats
```

---

## 🎉 恭喜！

您已经掌握了TPTP双向转换系统的基本使用方法。

**下一步建议：**
1. 尝试不同的AI模型，找到最适合您需求的配置
2. 阅读详细的操作指南了解高级功能
3. 根据您的具体需求调整参数设置

**需要帮助？**
- 查看 `OPERATION_GUIDE.md` 获取详细说明
- 使用 `--help` 参数查看命令选项
- 检查 `result/` 目录中的详细结果

祝您使用愉快！🚀
