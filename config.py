"""
配置文件 - 管理项目的各种配置参数
"""

import os
from typing import Dict, List


class Config:
    """配置管理类"""
    
    # AI模型提供商配置
    AI_PROVIDER = os.getenv("AI_PROVIDER", "openai")  # 可选: openai, doubao, deepseek

    # OpenAI API 配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "your-openai-api-key-here")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4.1-nano")
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", None)  # 自定义API端点

    # Doubao API 配置
    DOUBAO_API_KEY = os.getenv("ARK_API_KEY", "your-doubao-api-key-here")
    DOUBAO_MODEL = os.getenv("DOUBAO_MODEL", "doubao-seed-1-6-250615")
    DOUBAO_BASE_URL = os.getenv("DOUBAO_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")

    # DeepSeek R1 API 配置（使用字节跳动部署）
    DEEPSEEK_API_KEY = os.getenv("ARK_API_KEY", "your-deepseek-api-key-here")  # 使用相同的ARK_API_KEY
    DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-r1-250528")
    DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")

    MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))


    # TPTP处理配置
    TPTP_DIR = "./tptp"
    NL_DIR = "./nl"
    TRANSFORM_TPTP_DIR = "./transform_tptp"  # GPT转换后的TPTP文件
    RESULT_DIR = "./result"  # 一致性判定结果文件
    AXIOMS_SUBDIR = "Axioms"  # 公理文件子目录

    # 新增目录配置
    PREPROCESSED_NL_DIR = "./preprocessed_nl"  # 预处理后的自然语言文件
    FIX_STEPS_DIR = "./fix_steps"              # TPTP修复步骤文件

    # EProver配置
    EPROVER_PATH = "./prover/eprover"
    MAX_FIX_ATTEMPTS = int(os.getenv("MAX_FIX_ATTEMPTS", "5"))  # 最大修复尝试次数（可配置）
    EPROVER_TIMEOUT = 20  # eprover执行超时时间（秒）

    # 自然语言预处理配置
    ENABLE_NL_PREPROCESSING = os.getenv("ENABLE_NL_PREPROCESSING", "false").lower() == "true"  # 是否启用自然语言预处理

    # 验证结果分类配置
    ENABLE_VALIDATION_CLASSIFICATION = os.getenv("ENABLE_VALIDATION_CLASSIFICATION", "true").lower() == "true"  # 是否启用验证结果分类
    
    # 支持的文件扩展名
    SUPPORTED_EXTENSIONS = {
        '.p', '.ax', '.nl', '.txt', '.md'
    }
    
    # ChatGPT 请求参数
    DEFAULT_TEMPERATURE = 0.7
    DEFAULT_MAX_TOKENS = None

    @classmethod
    def get_ai_config(cls) -> Dict:
        """获取AI模型配置（支持OpenAI、Doubao和DeepSeek R1）"""
        # 动态获取当前的AI提供商（支持环境变量覆盖）
        current_provider = os.getenv("AI_PROVIDER", cls.AI_PROVIDER)

        base_config = {
            "provider": current_provider,
            "temperature": cls.DEFAULT_TEMPERATURE,
            "max_tokens": cls.DEFAULT_MAX_TOKENS,
            "max_retries": cls.MAX_RETRIES
        }

        if current_provider.lower() == "doubao":
            config = {
                **base_config,
                "api_key": os.getenv("ARK_API_KEY", cls.DOUBAO_API_KEY),
                "model": os.getenv("DOUBAO_MODEL", cls.DOUBAO_MODEL),
                "base_url": os.getenv("DOUBAO_BASE_URL", cls.DOUBAO_BASE_URL)
            }
        elif current_provider.lower() == "deepseek":
            config = {
                **base_config,
                "api_key": os.getenv("ARK_API_KEY", cls.DEEPSEEK_API_KEY),
                "model": os.getenv("DEEPSEEK_MODEL", cls.DEEPSEEK_MODEL),
                "base_url": os.getenv("DEEPSEEK_BASE_URL", cls.DEEPSEEK_BASE_URL),
                "timeout": 300  # DeepSeek R1需要更长的超时时间
            }
        else:  # 默认使用OpenAI
            config = {
                **base_config,
                "api_key": os.getenv("OPENAI_API_KEY", cls.OPENAI_API_KEY),
                "model": os.getenv("OPENAI_MODEL", cls.OPENAI_MODEL)
            }

            openai_base_url = os.getenv("OPENAI_BASE_URL", cls.OPENAI_BASE_URL)
            if openai_base_url:
                config["base_url"] = openai_base_url

        return config

    @classmethod
    def get_chatgpt_config(cls) -> Dict:
        """获取ChatGPT配置（向后兼容）"""
        return cls.get_ai_config()

    @classmethod
    def get_tptp_config(cls) -> Dict:
        """获取TPTP处理器配置"""
        return {
            "tptp_dir": cls.TPTP_DIR,
            "nl_dir": cls.NL_DIR,
            "axioms_subdir": cls.AXIOMS_SUBDIR
        }

    @classmethod
    def get_nl_to_tptp_config(cls) -> Dict:
        """获取自然语言到TPTP转换器配置"""
        return {
            "nl_dir": cls.NL_DIR,
            "transform_tptp_dir": cls.TRANSFORM_TPTP_DIR,
            "result_dir": cls.RESULT_DIR,
            "eprover_path": cls.EPROVER_PATH,
            "max_fix_attempts": cls.MAX_FIX_ATTEMPTS,
            "eprover_timeout": cls.EPROVER_TIMEOUT,
            "enable_nl_preprocessing": cls.ENABLE_NL_PREPROCESSING,
            "enable_validation_classification": cls.ENABLE_VALIDATION_CLASSIFICATION,
            "preprocessed_nl_dir": cls.PREPROCESSED_NL_DIR,
            "fix_steps_dir": cls.FIX_STEPS_DIR
        }
    
    @classmethod
    def validate_config(cls, ai_provider_override=None) -> List[str]:
        """验证配置，返回错误信息列表"""
        errors = []

        # 使用覆盖的提供商或当前环境变量中的提供商
        current_provider = ai_provider_override or os.getenv("AI_PROVIDER", cls.AI_PROVIDER)

        # 动态获取API密钥（支持环境变量覆盖）
        openai_key = os.getenv("OPENAI_API_KEY", cls.OPENAI_API_KEY)
        ark_key = os.getenv("ARK_API_KEY", cls.DOUBAO_API_KEY)

        # 根据当前AI提供商验证相应的API密钥
        if current_provider.lower() == "openai":
            if openai_key == "your-openai-api-key-here" or not openai_key:
                errors.append("请设置有效的 OPENAI_API_KEY")
        elif current_provider.lower() in ["doubao", "deepseek"]:
            if ark_key == "your-doubao-api-key-here" or not ark_key:
                errors.append("请设置有效的 ARK_API_KEY（用于Doubao/DeepSeek模型）")
        else:
            # 如果没有指定提供商，检查是否至少有一个API密钥可用
            has_openai = openai_key and openai_key != "your-openai-api-key-here"
            has_ark = ark_key and ark_key != "your-doubao-api-key-here"

            if not has_openai and not has_ark:
                errors.append("请设置至少一个有效的API密钥：OPENAI_API_KEY 或 ARK_API_KEY")

        if not os.path.exists(cls.TPTP_DIR):
            errors.append(f"TPTP目录不存在: {cls.TPTP_DIR}")

        return errors


# 预定义的提示词模板
class PromptTemplates:
    """提示词模板类"""

    TPTP_TO_NL = """
请将以下TPTP（Thousands of Problems for Theorem Provers）格式的逻辑公式转换为清晰的自然语言描述。

文件名: {filename}

TPTP内容:
{content}

请按照以下要求进行转换：
1. **保持逻辑结构**: 准确表达原始逻辑关系
2. **使用自然语言**: 转换为易于理解的自然语言
3. **保留重要信息**: 包括公理名称、定理名称、角色等
4. **组织结构**: 按照公理、定义、定理、猜想等分类组织
5. **添加解释**: 对复杂的逻辑关系提供简要解释

请使用中文进行描述，保持清晰的段落结构。
"""

    NL_TO_TPTP = """
请将以下自然语言描述转换为标准的TPTP（Thousands of Problems for Theorem Provers）格式。

自然语言内容:
{content}

转换要求：
1. **严格遵循TPTP语法**: 使用正确的fof()和cnf()语法
2. **保持逻辑完整性**: 确保所有逻辑关系都正确表达
3. **使用标准角色**: axiom, hypothesis, definition, conjecture等
4. **正确的量词**: ![X] (全称量词), ?[X] (存在量词)
5. **正确的连接词**: & (与), | (或), => (蕴含), <=> (等价), ~ (非)
6. **括号匹配**: 确保所有括号正确匹配
7. **语句结束**: 每个fof语句以点号(.)结束

输出格式：
- 只输出TPTP格式的内容
- 包含必要的注释（以%开头）
- 确保语法完全正确

请开始转换：
"""

    TPTP_FIX = """
你需要修复以下TPTP公式中的语法错误。请仔细分析EProver的完整验证信息，在不改变公式语义的前提下修复语法问题。

=== 完整TPTP公式 ===
{tptp_content}

=== EProver完整验证输出 ===
标准输出：
{eprover_stdout}

错误输出：
{eprover_stderr}

=== 错误分析 ===
{error_message}

=== 修复指导原则 ===
1. **保持语义不变**：绝对不能改变公式的逻辑含义
2. **精确定位错误**：根据EProver指出的行号和列号精确定位问题
3. **常见语法错误类型及修复方法**：
   - **函数/谓词参数数量不匹配（arity错误）**：
     * 错误示例：set_union2(A,B,C) 但定义为2个参数
     * 修复：要么改为set_union2(A,B)，要么重新定义为3参数函数
   - **括号不匹配或缺失**：
     * 确保每个开括号都有对应的闭括号
     * 检查量词、逻辑连接符的括号配对
   - **量词作用域问题**：
     * 格式：![X]: (formula) 或 ?[X]: (formula)
     * 确保量词正确绑定变量
   - **自由变量问题**：
     * 确保所有变量都被适当量化
     * 检查变量名的一致性

4. **TPTP语法要求**：
   - fof语句格式：fof(name, role, formula).
   - 每个语句必须以点号结束
   - 函数调用必须与声明的参数数量一致
   - 变量名通常大写，常量和函数名小写

请输出修复后的完整TPTP内容，确保：
- 修复所有EProver指出的语法错误
- 保持原有的逻辑语义完全不变
- 使用正确的TPTP语法格式
- 只输出修复后的TPTP代码，不要包含解释文字
"""

    NL_PREPROCESSING = """
请对以下自然语言描述进行预处理，简化表达并确保无歧义，为后续转换为TPTP格式做准备。

原始自然语言内容:
{content}

预处理要求：
1. **保留核心逻辑信息**：确保所有重要的逻辑关系、规则和约束都被保留
2. **简化复杂表达**：将复杂的自然语言表达转换为更直接、清晰的形式
3. **消除歧义**：识别并澄清可能存在歧义的表达
4. **标准化术语**：统一使用一致的术语和概念名称
5. **明确量词范围**：清楚地表达"所有"、"存在"、"如果...那么"等逻辑关系
6. **结构化组织**：按照公理、定义、规则、目标等逻辑结构重新组织内容

输出格式要求：
- 使用清晰的段落结构
- 用"公理："、"规则："、"定义："、"目标："等标签明确分类
- 每个逻辑语句独立成行
- 使用标准的逻辑表达方式

示例转换：
原文："在某些情况下，如果一个集合包含另一个集合的所有元素，我们可能会说第一个集合是第二个集合的超集"
简化："定义：如果集合A包含集合B的所有元素，那么A是B的超集"

请开始预处理：
"""


# 系统消息模板
class SystemMessages:
    """系统消息模板类"""

    TPTP_EXPERT = """你是一个专业的逻辑学和形式化验证专家，精通TPTP格式和一阶逻辑。你的任务是将TPTP格式的逻辑公式准确地转换为清晰易懂的自然语言描述，同时保持逻辑的严谨性和完整性。

你需要：
1. 准确理解TPTP语法和语义
2. 将形式化逻辑转换为自然语言，但不丢失逻辑精确性
3. 使用清晰的中文表达
4. 保持逻辑结构的层次性和组织性
5. 对复杂概念提供适当的解释"""

    NL_TO_TPTP_EXPERT = """你是一个专业的形式化逻辑专家，精通TPTP格式和一阶逻辑。你的任务是将自然语言描述准确地转换为符合TPTP标准的形式化逻辑表达式。

你必须：
1. 严格遵循TPTP语法规范
2. 确保生成的代码能通过eprover验证
3. 保持逻辑的准确性和完整性
4. 使用正确的量词、连接词和语法结构
5. 生成的代码必须语法正确，无任何错误"""

    TPTP_FIX_EXPERT = """你是一个TPTP语法专家，专门负责修复TPTP代码中的语法错误。你必须：

1. 仔细分析eprover提供的错误信息
2. 准确定位语法错误的位置
3. 进行最小化的修复，不改变原有逻辑
4. 确保修复后的代码完全符合TPTP语法规范
5. 生成能通过eprover验证的正确代码

你对TPTP语法有深入的理解，能够快速识别和修复各种语法错误。"""

    NL_PREPROCESSING_EXPERT = """你是一个专业的逻辑分析和自然语言处理专家，擅长将复杂的自然语言描述转换为清晰、无歧义的逻辑表达。

你的专长包括：
1. 识别和澄清自然语言中的歧义表达
2. 提取和保留核心逻辑信息
3. 标准化术语和概念
4. 结构化组织逻辑内容
5. 为形式化逻辑转换做准备

你必须确保预处理后的内容：
- 逻辑关系清晰明确
- 术语使用一致
- 结构组织合理
- 便于后续的TPTP转换"""


# TPTP文件类型映射
FILE_TYPE_MAPPING = {
    '.p': 'TPTP Problem',
    '.ax': 'TPTP Axioms',
    '.nl': 'Natural Language',
    '.txt': 'Text',
    '.md': 'Markdown'
}
