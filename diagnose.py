#!/usr/bin/env python3
"""
TPTP双向转换系统诊断脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append('.')

def check_environment():
    """检查环境配置"""
    print("🔍 环境检查")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查API密钥
    openai_key = os.getenv("OPENAI_API_KEY")
    ark_key = os.getenv("ARK_API_KEY")
    
    print(f"OPENAI_API_KEY: {'✓ 已设置' if openai_key and openai_key != 'your-openai-api-key-here' else '✗ 未设置'}")
    print(f"ARK_API_KEY: {'✓ 已设置' if ark_key and ark_key != 'your-doubao-api-key-here' else '✗ 未设置'}")
    
    # 检查AI_PROVIDER
    ai_provider = os.getenv("AI_PROVIDER", "openai")
    print(f"AI_PROVIDER: {ai_provider}")

def check_files():
    """检查文件结构"""
    print("\n📁 文件结构检查")
    print("=" * 50)
    
    directories = ["tptp", "nl", "transform_tptp", "result", "prover"]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        if dir_path.exists():
            if dir_name == "tptp":
                tptp_files = list(dir_path.glob("*.p"))
                print(f"✓ {dir_name}/: {len(tptp_files)} 个TPTP文件")
            elif dir_name == "nl":
                nl_files = list(dir_path.glob("*.nl"))
                print(f"✓ {dir_name}/: {len(nl_files)} 个自然语言文件")
            elif dir_name == "transform_tptp":
                transform_files = list(dir_path.glob("*.p"))
                print(f"✓ {dir_name}/: {len(transform_files)} 个转换后TPTP文件")
            elif dir_name == "result":
                result_files = list(dir_path.glob("*.txt"))
                print(f"✓ {dir_name}/: {len(result_files)} 个结果文件")
            elif dir_name == "prover":
                eprover_path = dir_path / "eprover"
                if eprover_path.exists():
                    print(f"✓ {dir_name}/: EProver {'可执行' if os.access(eprover_path, os.X_OK) else '不可执行'}")
                else:
                    print(f"✗ {dir_name}/: EProver不存在")
        else:
            print(f"✗ {dir_name}/: 目录不存在")

def check_config():
    """检查配置"""
    print("\n⚙️ 配置检查")
    print("=" * 50)
    
    try:
        from config import Config
        
        # 检查不同模型的配置
        for provider in ["openai", "doubao", "deepseek"]:
            print(f"\n{provider.upper()} 配置:")
            
            # 临时设置环境变量
            original_provider = os.environ.get("AI_PROVIDER")
            os.environ["AI_PROVIDER"] = provider
            
            try:
                # 重新加载配置以获取正确的模型信息
                import importlib
                import config
                importlib.reload(config)
                from config import Config as ReloadedConfig

                ai_config = ReloadedConfig.get_ai_config()
                print(f"  模型: {ai_config['model']}")
                print(f"  API密钥: {'✓ 已设置' if ai_config['api_key'] and ai_config['api_key'] != f'your-{provider}-api-key-here' else '✗ 未设置'}")
                if 'base_url' in ai_config:
                    print(f"  基础URL: {ai_config['base_url']}")

                # 验证配置
                errors = ReloadedConfig.validate_config(provider)
                if errors:
                    print(f"  验证: ✗ {'; '.join(errors)}")
                else:
                    print(f"  验证: ✓ 通过")
                    
            finally:
                # 恢复环境变量
                if original_provider is not None:
                    os.environ["AI_PROVIDER"] = original_provider
                else:
                    os.environ.pop("AI_PROVIDER", None)
                    
    except Exception as e:
        print(f"✗ 配置检查失败: {e}")

def check_eprover():
    """检查EProver"""
    print("\n🔧 EProver检查")
    print("=" * 50)
    
    try:
        from eprover_validator import EProverValidator
        from config import Config

        nl_config = Config.get_nl_to_tptp_config()
        validator = EProverValidator(eprover_path=nl_config["eprover_path"])
        
        if validator.is_available():
            version = validator.get_version()
            print(f"✓ EProver可用: {version}")
            
            # 测试简单验证
            test_tptp = "fof(test, axiom, human(socrates))."
            is_valid, stdout, stderr = validator.validate_tptp_content(test_tptp, "test.p")
            print(f"✓ 验证测试: {'通过' if is_valid else '失败'}")
            
        else:
            print("✗ EProver不可用")
            
    except Exception as e:
        print(f"✗ EProver检查失败: {e}")

def check_ai_clients():
    """检查AI客户端"""
    print("\n🤖 AI客户端检查")
    print("=" * 50)
    
    try:
        from ai_client import AIClient
        from config import Config
        
        # 检查可用的模型
        available_models = []
        
        # 检查OpenAI
        if os.getenv("OPENAI_API_KEY") and os.getenv("OPENAI_API_KEY") != "your-openai-api-key-here":
            try:
                openai_config = {
                    "provider": "openai",
                    "api_key": os.getenv("OPENAI_API_KEY"),
                    "model": "gpt-3.5-turbo"
                }
                client = AIClient.create_from_config(openai_config)
                print("✓ OpenAI客户端: 可创建")
                available_models.append("openai")
            except Exception as e:
                print(f"✗ OpenAI客户端: {e}")
        else:
            print("- OpenAI客户端: 未配置API密钥")
        
        # 检查Doubao/DeepSeek
        if os.getenv("ARK_API_KEY") and os.getenv("ARK_API_KEY") != "your-doubao-api-key-here":
            try:
                doubao_config = {
                    "provider": "doubao",
                    "api_key": os.getenv("ARK_API_KEY"),
                    "model": "doubao-seed-1-6-250615",
                    "base_url": "https://ark.cn-beijing.volces.com/api/v3"
                }
                client = AIClient.create_from_config(doubao_config)
                print("✓ Doubao客户端: 可创建")
                available_models.append("doubao")
                
                deepseek_config = {
                    "provider": "deepseek",
                    "api_key": os.getenv("ARK_API_KEY"),
                    "model": "deepseek-r1-250528",
                    "base_url": "https://ark.cn-beijing.volces.com/api/v3"
                }
                client = AIClient.create_from_config(deepseek_config)
                print("✓ DeepSeek客户端: 可创建")
                available_models.append("deepseek")
            except Exception as e:
                print(f"✗ Doubao/DeepSeek客户端: {e}")
        else:
            print("- Doubao/DeepSeek客户端: 未配置API密钥")
        
        print(f"\n可用模型: {', '.join(available_models) if available_models else '无'}")
        
    except Exception as e:
        print(f"✗ AI客户端检查失败: {e}")

def run_quick_test():
    """运行快速测试"""
    print("\n🧪 快速功能测试")
    print("=" * 50)
    
    # 检查是否有可用的API密钥
    has_openai = os.getenv("OPENAI_API_KEY") and os.getenv("OPENAI_API_KEY") != "your-openai-api-key-here"
    has_ark = os.getenv("ARK_API_KEY") and os.getenv("ARK_API_KEY") != "your-doubao-api-key-here"
    
    if not has_openai and not has_ark:
        print("⚠️ 没有可用的API密钥，跳过功能测试")
        return
    
    # 选择可用的模型
    test_model = "openai" if has_openai else "doubao"
    
    try:
        # 测试统计功能
        print(f"测试统计功能...")
        import subprocess
        result = subprocess.run([
            sys.executable, "tptp_to_nl.py", 
            "--model-provider", test_model, 
            "--stats"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ 统计功能: 正常")
        else:
            print(f"✗ 统计功能: 失败 - {result.stderr}")
            
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")

def main():
    """主函数"""
    print("🔍 TPTP双向转换系统诊断")
    print("=" * 60)
    
    check_environment()
    check_files()
    check_config()
    check_eprover()
    check_ai_clients()
    run_quick_test()
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)
    
    print("\n💡 如果发现问题，请参考:")
    print("  - QUICK_START.md - 快速入门指南")
    print("  - OPERATION_GUIDE.md - 详细操作说明")
    print("  - ./setup.sh - 自动化设置脚本")

if __name__ == "__main__":
    main()
